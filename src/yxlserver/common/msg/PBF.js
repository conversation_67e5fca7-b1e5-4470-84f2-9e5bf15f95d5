var PBF = (function () {
	    var valuesById = {}, values = Object.create(valuesById);
	values[valuesById[0] = "Default_ID"] = 0;
	values[valuesById[1] = "C2SLogin_ID"] = 1;
	values[valuesById[2] = "S2CLogin_ID"] = 2;
	values[valuesById[3] = "S2CRoleInfo_ID"] = 3;
	values[valuesById[4] = "S2CServerInfo_ID"] = 4;
	values[valuesById[5] = "C2SGM_ID"] = 5;
	values[valuesById[6] = "S2CKill_ID"] = 6;
	values[valuesById[7] = "C2SRoleBaseInfo_ID"] = 7;
	values[valuesById[8] = "S2CRoleBaseInfo_ID"] = 8;
	values[valuesById[9] = "S2CRoleData_ID"] = 9;
	values[valuesById[10] = "C2SOtherInfo_ID"] = 10;
	values[valuesById[11] = "S2COtherInfo_ID"] = 11;
	values[valuesById[12] = "S2CNotice_ID"] = 12;
	values[valuesById[13] = "C2SRespect_ID"] = 13;
	values[valuesById[14] = "S2CRespect_ID"] = 14;
	values[valuesById[15] = "S2COfflinePrize_ID"] = 15;
	values[valuesById[16] = "C2SGetOfflinePrize_ID"] = 16;
	values[valuesById[17] = "S2CGetOfflinePrize_ID"] = 17;
	values[valuesById[18] = "C2SGetGift1_ID"] = 18;
	values[valuesById[19] = "S2CGetGift1_ID"] = 19;
	values[valuesById[20] = "C2SGetGift2_ID"] = 20;
	values[valuesById[21] = "S2CGetGift2_ID"] = 21;
	values[valuesById[22] = "Ping_ID"] = 22;
	values[valuesById[23] = "Pong_ID"] = 23;
	values[valuesById[24] = "LoginFinish_ID"] = 24;
	values[valuesById[25] = "C2SAchieveShow_ID"] = 25;
	values[valuesById[26] = "S2CAchieveShow_ID"] = 26;
	values[valuesById[27] = "C2SUserInfo_ID"] = 27;
	values[valuesById[28] = "S2CUserInfo_ID"] = 28;
	values[valuesById[29] = "C2SLoginEnd_ID"] = 29;
	values[valuesById[30] = "S2CReportFcm_ID"] = 30;
	values[valuesById[31] = "C2SSetFcm_ID"] = 31;
	values[valuesById[32] = "C2SFocusPrice_ID"] = 32;
	values[valuesById[33] = "S2CFocusPrice_ID"] = 33;
	values[valuesById[34] = "C2SFirstInvite_ID"] = 34;
	values[valuesById[35] = "S2CFirstInvite_ID"] = 35;
	values[valuesById[36] = "C2SNewStory_ID"] = 36;
	values[valuesById[37] = "S2CNewStory_ID"] = 37;
	values[valuesById[38] = "S2KCrossPing_ID"] = 38;
	values[valuesById[39] = "S2CAllData_ID"] = 39;
	values[valuesById[40] = "K2SNewEvent_ID"] = 40;
	values[valuesById[41] = "C2SAutoJump_ID"] = 41;
	values[valuesById[50] = "C2SChangeMap_ID"] = 50;
	values[valuesById[51] = "S2CChangeMap_ID"] = 51;
	values[valuesById[52] = "C2SStartMove_ID"] = 52;
	values[valuesById[53] = "S2CStartMove_ID"] = 53;
	values[valuesById[54] = "S2CPlayerMove_ID"] = 54;
	values[valuesById[55] = "C2SStopMove_ID"] = 55;
	values[valuesById[56] = "S2CPlayerStopMove_ID"] = 56;
	values[valuesById[57] = "S2CPlayerEnterMap_ID"] = 57;
	values[valuesById[58] = "S2CPlayerLeaveMap_ID"] = 58;
	values[valuesById[59] = "S2CMonsterEnterMap_ID"] = 59;
	values[valuesById[60] = "S2CMonsterLeaveMap_ID"] = 60;
	values[valuesById[61] = "C2SStartFight_ID"] = 61;
	values[valuesById[62] = "S2CStartFight_ID"] = 62;
	values[valuesById[63] = "S2CUpMonsterInfo_ID"] = 63;
	values[valuesById[64] = "S2CTransfer_ID"] = 64;
	values[valuesById[65] = "C2SCheckFight_ID"] = 65;
	values[valuesById[66] = "S2CCheckFight_ID"] = 66;
	values[valuesById[67] = "S2CStopMove_ID"] = 67;
	values[valuesById[71] = "C2SRoleSkillUp_ID"] = 71;
	values[valuesById[72] = "C2SRoleSkillUpAuto_ID"] = 72;
	values[valuesById[73] = "S2CRoleSkillUp_ID"] = 73;
	values[valuesById[74] = "C2SRoleSkillOrder_ID"] = 74;
	values[valuesById[75] = "S2CRoleSkillOrder_ID"] = 75;
	values[valuesById[81] = "C2SUnlockSkin_ID"] = 81;
	values[valuesById[82] = "S2CUnlockSkin_ID"] = 82;
	values[valuesById[83] = "C2SWearSkin_ID"] = 83;
	values[valuesById[84] = "S2CWearSkin_ID"] = 84;
	values[valuesById[85] = "C2SGetVipPrize_ID"] = 85;
	values[valuesById[86] = "S2CGetVipPrize_ID"] = 86;
	values[valuesById[87] = "C2SGetNewPrize_ID"] = 87;
	values[valuesById[88] = "S2CGetNewPrize_ID"] = 88;
	values[valuesById[89] = "C2SClientSwitch_ID"] = 89;
	values[valuesById[90] = "S2CClientSwitch_ID"] = 90;
	values[valuesById[91] = "C2SReLogin_ID"] = 91;
	values[valuesById[92] = "S2CReLogin_ID"] = 92;
	values[valuesById[93] = "C2SStartCollect_ID"] = 93;
	values[valuesById[94] = "S2CStartCollect_ID"] = 94;
	values[valuesById[95] = "C2SEndCollect_ID"] = 95;
	values[valuesById[96] = "S2CEndCollect_ID"] = 96;
	values[valuesById[97] = "C2SShareSuccess_ID"] = 97;
	values[valuesById[98] = "S2CMonsterTalk_ID"] = 98;
	values[valuesById[99] = "C2SNewPlayerZF_ID"] = 99;
	values[valuesById[100] = "S2CPrizeReport_ID"] = 100;
	values[valuesById[101] = "S2CBattlefieldReport_ID"] = 101;
	values[valuesById[102] = "C2SEndFight_ID"] = 102;
	values[valuesById[103] = "C2SStageFight_ID"] = 103;
	values[valuesById[104] = "S2CStageFight_ID"] = 104;
	values[valuesById[105] = "C2SAutoStage_ID"] = 105;
	values[valuesById[106] = "C2SStageSeek_ID"] = 106;
	values[valuesById[107] = "S2CStageSeek_ID"] = 107;
	values[valuesById[108] = "C2SStageHelp_ID"] = 108;
	values[valuesById[109] = "S2CStageHelp_ID"] = 109;
	values[valuesById[110] = "S2CStagePrize_ID"] = 110;
	values[valuesById[111] = "C2SGetStagePrize_ID"] = 111;
	values[valuesById[112] = "S2CGetStagePrize_ID"] = 112;
	values[valuesById[113] = "C2SCatchPet_ID"] = 113;
	values[valuesById[114] = "S2CCatchPet_ID"] = 114;
	values[valuesById[115] = "C2SStagePrize_ID"] = 115;
	values[valuesById[116] = "C2SGetAllStagePrize_ID"] = 116;
	values[valuesById[117] = "S2CGetAllStagePrize_ID"] = 117;
	values[valuesById[120] = "C2SRealName_ID"] = 120;
	values[valuesById[121] = "S2CRealName_ID"] = 121;
	values[valuesById[122] = "C2SGetRealNamePrize_ID"] = 122;
	values[valuesById[123] = "S2CGetRealNamePrize_ID"] = 123;
	values[valuesById[124] = "C2SGetLimitCloth_ID"] = 124;
	values[valuesById[125] = "S2CGetLimitCloth_ID"] = 125;
	values[valuesById[126] = "S2CGetLimitClothEnd_ID"] = 126;
	values[valuesById[127] = "C2SSkinUp_ID"] = 127;
	values[valuesById[128] = "S2CSkinUp_ID"] = 128;
	values[valuesById[130] = "C2SBuyVipPrize_ID"] = 130;
	values[valuesById[131] = "S2CBuyVipPrize_ID"] = 131;
	values[valuesById[201] = "C2SGradeUp_ID"] = 201;
	values[valuesById[202] = "S2CGradeUp_ID"] = 202;
	values[valuesById[203] = "C2SRoleLevelUp_ID"] = 203;
	values[valuesById[204] = "S2CRoleLevelUp_ID"] = 204;
	values[valuesById[205] = "C2SEquipGradeUp_ID"] = 205;
	values[valuesById[206] = "S2CEquipGradeUp_ID"] = 206;
	values[valuesById[207] = "C2SGrade2_ID"] = 207;
	values[valuesById[208] = "S2CGrade2_ID"] = 208;
	values[valuesById[210] = "S2CUserGrade_ID"] = 210;
	values[valuesById[211] = "C2SRoleGodGradeUp_ID"] = 211;
	values[valuesById[212] = "S2CRoleGodGradeUp_ID"] = 212;
	values[valuesById[213] = "C2SGetRoleGodPrize_ID"] = 213;
	values[valuesById[214] = "S2CGetRoleGodPrize_ID"] = 214;
	values[valuesById[215] = "S2CRoleGodPrize_ID"] = 215;
	values[valuesById[302] = "S2CGetFriends_ID"] = 302;
	values[valuesById[303] = "C2SFocus_ID"] = 303;
	values[valuesById[304] = "S2CFocus_ID"] = 304;
	values[valuesById[305] = "C2SCancelFocus_ID"] = 305;
	values[valuesById[306] = "S2CCancelFocus_ID"] = 306;
	values[valuesById[307] = "C2SHate_ID"] = 307;
	values[valuesById[308] = "S2CHate_ID"] = 308;
	values[valuesById[309] = "C2SCancelHate_ID"] = 309;
	values[valuesById[310] = "S2CCancelHate_ID"] = 310;
	values[valuesById[311] = "C2SGiveCoin_ID"] = 311;
	values[valuesById[312] = "S2CGiveCoin_ID"] = 312;
	values[valuesById[313] = "C2SGetCoin_ID"] = 313;
	values[valuesById[314] = "S2CGetCoin_ID"] = 314;
	values[valuesById[315] = "C2SGetSuggest_ID"] = 315;
	values[valuesById[316] = "S2CGetSuggest_ID"] = 316;
	values[valuesById[317] = "C2SOneKeyGiveCoin_ID"] = 317;
	values[valuesById[318] = "S2COneKeyGiveCoin_ID"] = 318;
	values[valuesById[319] = "C2SOneKeyGetCoin_ID"] = 319;
	values[valuesById[320] = "S2COneKeyGetCoin_ID"] = 320;
	values[valuesById[321] = "C2SOneKeyFocus_ID"] = 321;
	values[valuesById[322] = "S2COneKeyFocus_ID"] = 322;
	values[valuesById[350] = "S2CMasterMessage_ID"] = 350;
	values[valuesById[351] = "C2SSendMasterNotice_ID"] = 351;
	values[valuesById[352] = "S2CSendMasterNotice_ID"] = 352;
	values[valuesById[353] = "S2CMasterAds_ID"] = 353;
	values[valuesById[354] = "C2SMasterInvite_ID"] = 354;
	values[valuesById[355] = "S2CMasterInvite_ID"] = 355;
	values[valuesById[356] = "S2CInvitePupil_ID"] = 356;
	values[valuesById[357] = "C2SHandleMasterInvite_ID"] = 357;
	values[valuesById[358] = "S2CHandleMasterInvite_ID"] = 358;
	values[valuesById[359] = "C2SMasterGiveExp_ID"] = 359;
	values[valuesById[360] = "S2CMasterGiveExp_ID"] = 360;
	values[valuesById[361] = "C2SGetMasterExp_ID"] = 361;
	values[valuesById[362] = "S2CGetMasterExp_ID"] = 362;
	values[valuesById[363] = "C2SDeletePupil_ID"] = 363;
	values[valuesById[364] = "S2CDeletePupil_ID"] = 364;
	values[valuesById[365] = "C2SDeleteMaster_ID"] = 365;
	values[valuesById[366] = "S2CDeleteMaster_ID"] = 366;
	values[valuesById[367] = "C2SGetPupilTask_ID"] = 367;
	values[valuesById[368] = "S2CGetPupilTask_ID"] = 368;
	values[valuesById[370] = "S2CMarryStatus_ID"] = 370;
	values[valuesById[371] = "C2SGetMarryList_ID"] = 371;
	values[valuesById[372] = "S2CGetMarryList_ID"] = 372;
	values[valuesById[373] = "C2SGetMarry_ID"] = 373;
	values[valuesById[374] = "S2CGetMarry_ID"] = 374;
	values[valuesById[375] = "S2CSendMarry_ID"] = 375;
	values[valuesById[377] = "C2SSendFlower_ID"] = 377;
	values[valuesById[378] = "S2CSendFlower_ID"] = 378;
	values[valuesById[379] = "S2CGetFlower_ID"] = 379;
	values[valuesById[381] = "C2SMarryUpdate_ID"] = 381;
	values[valuesById[382] = "S2CMarryUpdate_ID"] = 382;
	values[valuesById[383] = "C2SHouseUpdate_ID"] = 383;
	values[valuesById[384] = "S2CHouseUpdate_ID"] = 384;
	values[valuesById[386] = "C2SGetUpdate_ID"] = 386;
	values[valuesById[387] = "S2CGetUpdate_ID"] = 387;
	values[valuesById[388] = "S2CNewMarry_ID"] = 388;
	values[valuesById[389] = "C2SSendMarryGift_ID"] = 389;
	values[valuesById[390] = "S2CSendMarryGift_ID"] = 390;
	values[valuesById[391] = "C2SHandelMarry_ID"] = 391;
	values[valuesById[392] = "S2CHandelMarry_ID"] = 392;
	values[valuesById[393] = "C2SDeleteWife_ID"] = 393;
	values[valuesById[394] = "S2CDeleteWife_ID"] = 394;
	values[valuesById[395] = "S2CMateOnline_ID"] = 395;
	values[valuesById[401] = "C2SGetHistoryChat_ID"] = 401;
	values[valuesById[402] = "S2CGetHistoryChat_ID"] = 402;
	values[valuesById[403] = "S2CNewChatMsg_ID"] = 403;
	values[valuesById[404] = "C2SSendChatMsg_ID"] = 404;
	values[valuesById[405] = "S2CSendChatMsg_ID"] = 405;
	values[valuesById[411] = "C2SWhisper_ID"] = 411;
	values[valuesById[412] = "S2CWhisper_ID"] = 412;
	values[valuesById[413] = "C2SGetWhisper_ID"] = 413;
	values[valuesById[414] = "S2CGetWhisper_ID"] = 414;
	values[valuesById[415] = "S2CAllUnreadWhisper_ID"] = 415;
	values[valuesById[416] = "C2SGetOnlineStatus_ID"] = 416;
	values[valuesById[417] = "S2CGetOnlineStatus_ID"] = 417;
	values[valuesById[418] = "C2SRemoveWhisper_ID"] = 418;
	values[valuesById[419] = "S2CRemoveWhisper_ID"] = 419;
	values[valuesById[420] = "C2SChangeNick_ID"] = 420;
	values[valuesById[421] = "S2CChangeNick_ID"] = 421;
	values[valuesById[430] = "C2SGetShopMallList_ID"] = 430;
	values[valuesById[431] = "S2CGetShopMallList_ID"] = 431;
	values[valuesById[432] = "C2SShopBuy_ID"] = 432;
	values[valuesById[433] = "S2CShopBuy_ID"] = 433;
	values[valuesById[434] = "C2SOrgMallList_ID"] = 434;
	values[valuesById[435] = "S2COrgMallList_ID"] = 435;
	values[valuesById[436] = "C2SOrgBuy_ID"] = 436;
	values[valuesById[437] = "S2COrgBuy_ID"] = 437;
	values[valuesById[438] = "C2SGetGoodsLimit_ID"] = 438;
	values[valuesById[439] = "S2CGetGoodsLimit_ID"] = 439;
	values[valuesById[440] = "C2SMailList_ID"] = 440;
	values[valuesById[441] = "S2CMailList_ID"] = 441;
	values[valuesById[444] = "C2SGetMailAttach_ID"] = 444;
	values[valuesById[445] = "S2CGetMailAttach_ID"] = 445;
	values[valuesById[446] = "S2CNewMail_ID"] = 446;
	values[valuesById[450] = "S2CQuizAsk_ID"] = 450;
	values[valuesById[451] = "C2SAnswerQuiz_ID"] = 451;
	values[valuesById[452] = "S2CAnswerQuiz_ID"] = 452;
	values[valuesById[453] = "S2CQuizSum_ID"] = 453;
	values[valuesById[454] = "S2CQuizRank_ID"] = 454;
	values[valuesById[455] = "S2CQuizFirst_ID"] = 455;
	values[valuesById[456] = "S2CQuizStart_ID"] = 456;
	values[valuesById[457] = "C2SDelMail_ID"] = 457;
	values[valuesById[458] = "S2CDelMail_ID"] = 458;
	values[valuesById[460] = "S2CWestExp_ID"] = 460;
	values[valuesById[461] = "C2SGetProtectPlayer_ID"] = 461;
	values[valuesById[462] = "S2CGetProtectPlayer_ID"] = 462;
	values[valuesById[463] = "C2SGetWestExp_ID"] = 463;
	values[valuesById[464] = "S2CGetWestExp_ID"] = 464;
	values[valuesById[465] = "C2SQuickFinishWestExp_ID"] = 465;
	values[valuesById[466] = "S2CQuickFinishWestExp_ID"] = 466;
	values[valuesById[467] = "S2CFinishWestExp_ID"] = 467;
	values[valuesById[468] = "C2SGetWestPrize_ID"] = 468;
	values[valuesById[469] = "S2CGetWestPrize_ID"] = 469;
	values[valuesById[470] = "C2SGetRobbedList_ID"] = 470;
	values[valuesById[471] = "S2CGetRobbedList_ID"] = 471;
	values[valuesById[472] = "C2SSendRevenge_ID"] = 472;
	values[valuesById[473] = "S2CSendRevenge_ID"] = 473;
	values[valuesById[474] = "C2SStartWestExp_ID"] = 474;
	values[valuesById[475] = "S2CStartWestExp_ID"] = 475;
	values[valuesById[476] = "C2SSendRob_ID"] = 476;
	values[valuesById[477] = "S2CSendRob_ID"] = 477;
	values[valuesById[478] = "S2CBeRob_ID"] = 478;
	values[valuesById[480] = "S2CWestExpStart_ID"] = 480;
	values[valuesById[481] = "S2CNewProtectPlayer_ID"] = 481;
	values[valuesById[482] = "S2CEndProtectPlayer_ID"] = 482;
	values[valuesById[500] = "C2SUserBag_ID"] = 500;
	values[valuesById[501] = "S2CUserBag_ID"] = 501;
	values[valuesById[504] = "C2SExtendBag_ID"] = 504;
	values[valuesById[505] = "S2CExtendBag_ID"] = 505;
	values[valuesById[502] = "C2SExchange_ID"] = 502;
	values[valuesById[503] = "S2CExchange_ID"] = 503;
	values[valuesById[506] = "C2SWearEquip_ID"] = 506;
	values[valuesById[507] = "S2CWearEquip_ID"] = 507;
	values[valuesById[508] = "C2SMeltEquip_ID"] = 508;
	values[valuesById[509] = "S2CMeltEquip_ID"] = 509;
	values[valuesById[510] = "C2SOpenTreasure_ID"] = 510;
	values[valuesById[511] = "S2COpenTreasure_ID"] = 511;
	values[valuesById[512] = "C2SAutoMelt_ID"] = 512;
	values[valuesById[513] = "S2CAutoMelt_ID"] = 513;
	values[valuesById[520] = "S2CBagChange_ID"] = 520;
	values[valuesById[521] = "C2SMeltGoldByItem_ID"] = 521;
	values[valuesById[522] = "S2CMeltGoldByItem_ID"] = 522;
	values[valuesById[523] = "C2SGetBattlePrize_ID"] = 523;
	values[valuesById[524] = "ItemFly_ID"] = 524;
	values[valuesById[525] = "C2SWearOneEquip_ID"] = 525;
	values[valuesById[526] = "S2CWearOneEquip_ID"] = 526;
	values[valuesById[527] = "C2SEquipStar_ID"] = 527;
	values[valuesById[528] = "S2CEquipStar_ID"] = 528;
	values[valuesById[530] = "C2SGoldBag_ID"] = 530;
	values[valuesById[531] = "S2CGoldBag_ID"] = 531;
	values[valuesById[532] = "C2SGetGoldBagInfo_ID"] = 532;
	values[valuesById[533] = "S2CGetGoldBagInfo_ID"] = 533;
	values[valuesById[534] = "C2SEquipStarInherit_ID"] = 534;
	values[valuesById[535] = "S2CEquipStarInherit_ID"] = 535;
	values[valuesById[540] = "S2CClientDb_ID"] = 540;
	values[valuesById[541] = "C2SClinetSet_ID"] = 541;
	values[valuesById[542] = "S2CClinetSet_ID"] = 542;
	values[valuesById[601] = "S2CBossPersonalInfo_ID"] = 601;
	values[valuesById[602] = "C2SBossPersonalFight_ID"] = 602;
	values[valuesById[603] = "S2CBossPersonalFight_ID"] = 603;
	values[valuesById[604] = "C2SBossPersonalSweep_ID"] = 604;
	values[valuesById[605] = "S2CBossPersonalSweep_ID"] = 605;
	values[valuesById[606] = "S2CInstanceMaterialInfo_ID"] = 606;
	values[valuesById[607] = "C2SInstanceMaterialFight_ID"] = 607;
	values[valuesById[608] = "S2CInstanceMaterialFight_ID"] = 608;
	values[valuesById[609] = "C2SInstanceMaterialSweep_ID"] = 609;
	values[valuesById[610] = "S2CInstanceMaterialSweep_ID"] = 610;
	values[valuesById[611] = "S2CInstanceTreasureInfo_ID"] = 611;
	values[valuesById[612] = "C2SInstanceTreasureFight_ID"] = 612;
	values[valuesById[613] = "S2CInstanceTreasureFight_ID"] = 613;
	values[valuesById[614] = "C2SInstanceTreasureSweep_ID"] = 614;
	values[valuesById[615] = "S2CInstanceTreasureSweep_ID"] = 615;
	values[valuesById[616] = "C2SGetInstanceTreasureBox_ID"] = 616;
	values[valuesById[617] = "S2CGetInstanceTreasureBox_ID"] = 617;
	values[valuesById[618] = "S2CInstanceHeavenlyInfo_ID"] = 618;
	values[valuesById[619] = "C2SInstanceHeavenlyFight_ID"] = 619;
	values[valuesById[620] = "S2CInstanceHeavenlyFight_ID"] = 620;
	values[valuesById[621] = "C2SInstanceHeavenlySweep_ID"] = 621;
	values[valuesById[622] = "S2CInstanceHeavenlySweep_ID"] = 622;
	values[valuesById[623] = "C2SGetInstanceHeavenlyBox_ID"] = 623;
	values[valuesById[624] = "S2CGetInstanceHeavenlyBox_ID"] = 624;
	values[valuesById[625] = "S2CInstanceTowerInfo_ID"] = 625;
	values[valuesById[626] = "C2SInstanceTowerFight_ID"] = 626;
	values[valuesById[627] = "S2CInstanceTowerFight_ID"] = 627;
	values[valuesById[631] = "S2CAllBossInfo_ID"] = 631;
	values[valuesById[632] = "C2SAllBossStart_ID"] = 632;
	values[valuesById[633] = "S2CAllBossStart_ID"] = 633;
	values[valuesById[634] = "C2SAllBossCfg_ID"] = 634;
	values[valuesById[635] = "C2SAllBossRelive_ID"] = 635;
	values[valuesById[636] = "S2CAllBossRelive_ID"] = 636;
	values[valuesById[637] = "C2SAllBossGetBuyInfo_ID"] = 637;
	values[valuesById[638] = "S2CAllBossGetBuyInfo_ID"] = 638;
	values[valuesById[639] = "C2SAllBossBuyTimes_ID"] = 639;
	values[valuesById[640] = "S2CAllBossBuyTimes_ID"] = 640;
	values[valuesById[641] = "C2SAllBossGetDamageLog_ID"] = 641;
	values[valuesById[642] = "S2CAllBossGetDamageLog_ID"] = 642;
	values[valuesById[643] = "C2SAllBossGetKillLog_ID"] = 643;
	values[valuesById[644] = "S2CAllBossGetKillLog_ID"] = 644;
	values[valuesById[651] = "S2CFieldBossInfo_ID"] = 651;
	values[valuesById[652] = "C2SFieldBossStart_ID"] = 652;
	values[valuesById[653] = "S2CFieldBossStart_ID"] = 653;
	values[valuesById[661] = "S2CBossVipInfo_ID"] = 661;
	values[valuesById[662] = "C2SBossVipStart_ID"] = 662;
	values[valuesById[663] = "S2CBossVipStart_ID"] = 663;
	values[valuesById[671] = "S2CBossHillData_ID"] = 671;
	values[valuesById[672] = "C2SBossHillFight_ID"] = 672;
	values[valuesById[673] = "S2CBossHillFight_ID"] = 673;
	values[valuesById[674] = "C2SBossHillReplace_ID"] = 674;
	values[valuesById[675] = "S2CBossHillReplace_ID"] = 675;
	values[valuesById[676] = "C2SBossHillOpen_ID"] = 676;
	values[valuesById[677] = "S2CBossHillOpen_ID"] = 677;
	values[valuesById[682] = "C2SFieldBossItem_ID"] = 682;
	values[valuesById[683] = "S2CFieldBossItem_ID"] = 683;
	values[valuesById[684] = "S2CInstanceDemonInfo_ID"] = 684;
	values[valuesById[685] = "C2SInstanceDemonFight_ID"] = 685;
	values[valuesById[686] = "S2CInstanceDemonFight_ID"] = 686;
	values[valuesById[687] = "C2SInstanceDemonSweep_ID"] = 687;
	values[valuesById[688] = "S2CInstanceDemonSweep_ID"] = 688;
	values[valuesById[689] = "C2SGetInstanceDemonBox_ID"] = 689;
	values[valuesById[690] = "S2CGetInstanceDemonBox_ID"] = 690;
	values[valuesById[691] = "C2SInstanceSoulHallFight_ID"] = 691;
	values[valuesById[692] = "S2CInstanceSoulHallFight_ID"] = 692;
	values[valuesById[693] = "C2SMixHolySoul_ID"] = 693;
	values[valuesById[694] = "S2CMixHolySoul_ID"] = 694;
	values[valuesById[695] = "C2SBreakHolySoul_ID"] = 695;
	values[valuesById[696] = "S2CBreakHolySoul_ID"] = 696;
	values[valuesById[697] = "C2SInjectHolySoul_ID"] = 697;
	values[valuesById[698] = "S2CInjectHolySoul_ID"] = 698;
	values[valuesById[699] = "C2SOutHolySoul_ID"] = 699;
	values[valuesById[700] = "S2COutHolySoul_ID"] = 700;
	values[valuesById[701] = "C2SHolyBeast_ID"] = 701;
	values[valuesById[702] = "S2CHolyBeast_ID"] = 702;
	values[valuesById[703] = "C2SGetTaskPrize_ID"] = 703;
	values[valuesById[704] = "S2CGetTaskPrize_ID"] = 704;
	values[valuesById[710] = "S2CLastDayRemain_ID"] = 710;
	values[valuesById[711] = "C2SLifeFind_ID"] = 711;
	values[valuesById[712] = "S2CLifeFind_ID"] = 712;
	values[valuesById[713] = "C2SGetHistoryTaskPrize_ID"] = 713;
	values[valuesById[714] = "S2CGetHistoryTaskPrize_ID"] = 714;
	values[valuesById[715] = "C2SPeaceFinish_ID"] = 715;
	values[valuesById[716] = "S2CPeaceFinish_ID"] = 716;
	values[valuesById[717] = "C2SLifeFastFind_ID"] = 717;
	values[valuesById[718] = "S2CLifeFastFind_ID"] = 718;
	values[valuesById[719] = "C2SWorldLevel_ID"] = 719;
	values[valuesById[720] = "S2CSMMonster_ID"] = 720;
	values[valuesById[721] = "C2SSMFight_ID"] = 721;
	values[valuesById[722] = "S2CSMFight_ID"] = 722;
	values[valuesById[723] = "C2SSMRefreshStar_ID"] = 723;
	values[valuesById[724] = "S2CSMRefreshStar_ID"] = 724;
	values[valuesById[725] = "C2SSMFastFinish_ID"] = 725;
	values[valuesById[726] = "S2CSMFastFinish_ID"] = 726;
	values[valuesById[730] = "C2SSignPrize_ID"] = 730;
	values[valuesById[731] = "S2CSignPrize_ID"] = 731;
	values[valuesById[732] = "C2SOneKeyInjectHolySoul_ID"] = 732;
	values[valuesById[733] = "S2COneKeyInjectHolySoul_ID"] = 733;
	values[valuesById[741] = "S2CHistoryTaskInfo_ID"] = 741;
	values[valuesById[750] = "C2SFairyCharacterData_ID"] = 750;
	values[valuesById[751] = "S2CFairyCharacterData_ID"] = 751;
	values[valuesById[752] = "C2SFairyCharacterFight_ID"] = 752;
	values[valuesById[753] = "S2CFairyCharacterFight_ID"] = 753;
	values[valuesById[754] = "C2SFairyMemoirPrize_ID"] = 754;
	values[valuesById[755] = "S2CFairyMemoirPrize_ID"] = 755;
	values[valuesById[756] = "C2SFairyCharacterSweep_ID"] = 756;
	values[valuesById[757] = "S2CFairyCharacterSweep_ID"] = 757;
	values[valuesById[758] = "C2SFairyAchieventPrize_ID"] = 758;
	values[valuesById[759] = "S2CFairyAchieventPrize_ID"] = 759;
	values[valuesById[780] = "S2CFairyAchievementChange_ID"] = 780;
	values[valuesById[781] = "S2CFairyMemoirTrigger_ID"] = 781;
	values[valuesById[801] = "C2SPartnerLevelUp_ID"] = 801;
	values[valuesById[802] = "S2CPartnerLevelUp_ID"] = 802;
	values[valuesById[803] = "C2SPartnerStarUp_ID"] = 803;
	values[valuesById[804] = "S2CPartnerStarUp_ID"] = 804;
	values[valuesById[805] = "C2SPartnerBattlePos_ID"] = 805;
	values[valuesById[806] = "S2CPartnerBattlePos_ID"] = 806;
	values[valuesById[807] = "C2SPartnerRefreshSkill_ID"] = 807;
	values[valuesById[808] = "S2CPartnerRefreshSkill_ID"] = 808;
	values[valuesById[809] = "C2SPartnerReplaceSkill_ID"] = 809;
	values[valuesById[810] = "S2CPartnerReplaceSkill_ID"] = 810;
	values[valuesById[811] = "C2SPartnerNick_ID"] = 811;
	values[valuesById[812] = "S2CPartnerNick_ID"] = 812;
	values[valuesById[813] = "C2SPartnerSuit_ID"] = 813;
	values[valuesById[814] = "S2CPartnerSuit_ID"] = 814;
	values[valuesById[815] = "C2SUnlockNewPartner_ID"] = 815;
	values[valuesById[816] = "S2CUnlockNewPartner_ID"] = 816;
	values[valuesById[817] = "C2SPartnerGradeUp_ID"] = 817;
	values[valuesById[818] = "S2CPartnerGradeUp_ID"] = 818;
	values[valuesById[819] = "C2SStickPartner_ID"] = 819;
	values[valuesById[820] = "S2CStickPartner_ID"] = 820;
	values[valuesById[821] = "S2CUserPet_ID"] = 821;
	values[valuesById[822] = "C2SPartnerSuitRobot_ID"] = 822;
	values[valuesById[831] = "S2CUserPetA_ID"] = 831;
	values[valuesById[841] = "S2CUserDevil_ID"] = 841;
	values[valuesById[842] = "C2SDevilAwake_ID"] = 842;
	values[valuesById[843] = "S2CDevilAwake_ID"] = 843;
	values[valuesById[844] = "C2SDevilLevelUp_ID"] = 844;
	values[valuesById[845] = "S2CDevilLevelUp_ID"] = 845;
	values[valuesById[846] = "C2SDevilPos_ID"] = 846;
	values[valuesById[847] = "S2CDevilPos_ID"] = 847;
	values[valuesById[851] = "Beauty_ID"] = 851;
	values[valuesById[852] = "C2SBeautyFlySwitch_ID"] = 852;
	values[valuesById[853] = "S2CBeautyFlySwitch_ID"] = 853;
	values[valuesById[854] = "C2SBeautyEquip_ID"] = 854;
	values[valuesById[855] = "S2CBeautyEquip_ID"] = 855;
	values[valuesById[856] = "C2SBeautyMagicRefresh_ID"] = 856;
	values[valuesById[857] = "S2CBeautyMagicRefresh_ID"] = 857;
	values[valuesById[858] = "C2SBeautyMagicChange_ID"] = 858;
	values[valuesById[859] = "S2CBeautyMagicChange_ID"] = 859;
	values[valuesById[860] = "C2SPartnerDelSkill_ID"] = 860;
	values[valuesById[861] = "S2CPartnerDelSkill_ID"] = 861;
	values[valuesById[862] = "C2SPartnerUpSkill_ID"] = 862;
	values[valuesById[863] = "S2CPartnerUpSkill_ID"] = 863;
	values[valuesById[864] = "S2CPartnerNewSkill_ID"] = 864;
	values[valuesById[871] = "C2SBeautyNewCloth_ID"] = 871;
	values[valuesById[872] = "S2CBeautyNewCloth_ID"] = 872;
	values[valuesById[873] = "C2SBeautyWearCloth_ID"] = 873;
	values[valuesById[874] = "S2CBeautyWearCloth_ID"] = 874;
	values[valuesById[875] = "C2SBeautyClothUp_ID"] = 875;
	values[valuesById[876] = "S2CBeautyClothUp_ID"] = 876;
	values[valuesById[880] = "S2CUserKid_ID"] = 880;
	values[valuesById[881] = "S2CKidTalentUp_ID"] = 881;
	values[valuesById[882] = "C2SKidTalentUp_ID"] = 882;
	values[valuesById[883] = "C2SKidUseTalent_ID"] = 883;
	values[valuesById[884] = "S2CKidUseTalent_ID"] = 884;
	values[valuesById[885] = "C2SKidGoodUp_ID"] = 885;
	values[valuesById[886] = "S2CKidGoodUp_ID"] = 886;
	values[valuesById[887] = "C2SToyLevelUp_ID"] = 887;
	values[valuesById[888] = "S2CToyLevelUp_ID"] = 888;
	values[valuesById[889] = "C2SToyOneKeyLevelUp_ID"] = 889;
	values[valuesById[890] = "S2CToyOneKeyLevelUp_ID"] = 890;
	values[valuesById[891] = "S2CUserToy_ID"] = 891;
	values[valuesById[895] = "C2SPartnerBattlePosRobot_ID"] = 895;
	values[valuesById[896] = "C2SGetPartner_ID"] = 896;
	values[valuesById[897] = "S2CGetPartner_ID"] = 897;
	values[valuesById[900] = "C2SAllRank_ID"] = 900;
	values[valuesById[901] = "S2CAllRank_ID"] = 901;
	values[valuesById[1001] = "S2CServerTime_ID"] = 1001;
	values[valuesById[1002] = "S2CServerAge_ID"] = 1002;
	values[valuesById[1101] = "C2SJJCList_ID"] = 1101;
	values[valuesById[1102] = "S2CJJCList_ID"] = 1102;
	values[valuesById[1103] = "C2SJJCFight_ID"] = 1103;
	values[valuesById[1104] = "S2CJJCFight_ID"] = 1104;
	values[valuesById[1105] = "C2SJJCBuyTimes_ID"] = 1105;
	values[valuesById[1106] = "S2CJJCBuyTimes_ID"] = 1106;
	values[valuesById[1107] = "C2SJJCGetBuyInfo_ID"] = 1107;
	values[valuesById[1108] = "S2CJJCGetBuyInfo_ID"] = 1108;
	values[valuesById[1111] = "S2CAllBossV2Info_ID"] = 1111;
	values[valuesById[1112] = "C2SAllBossV2Start_ID"] = 1112;
	values[valuesById[1113] = "S2CAllBossV2Start_ID"] = 1113;
	values[valuesById[1114] = "C2SAllBossV2Cfg_ID"] = 1114;
	values[valuesById[1117] = "C2SAllBossV2GetBuyInfo_ID"] = 1117;
	values[valuesById[1118] = "S2CAllBossV2GetBuyInfo_ID"] = 1118;
	values[valuesById[1119] = "C2SAllBossV2BuyTimes_ID"] = 1119;
	values[valuesById[1120] = "S2CAllBossV2BuyTimes_ID"] = 1120;
	values[valuesById[1121] = "C2SAllBossV2GetDamageLog_ID"] = 1121;
	values[valuesById[1122] = "S2CAllBossV2GetDamageLog_ID"] = 1122;
	values[valuesById[1125] = "C2SAllBossV2PlayerInBoss_ID"] = 1125;
	values[valuesById[1126] = "S2CAllBossV2PlayerInBoss_ID"] = 1126;
	values[valuesById[1127] = "K2SFightAllBossResult_ID"] = 1127;
	values[valuesById[1131] = "S2CFieldBossV2Info_ID"] = 1131;
	values[valuesById[1132] = "C2SFieldBossV2StartFight_ID"] = 1132;
	values[valuesById[1133] = "S2CFieldBossV2StartFight_ID"] = 1133;
	values[valuesById[1134] = "C2SFieldBossV2Cfg_ID"] = 1134;
	values[valuesById[1137] = "C2SFieldBossV2BuyTimes_ID"] = 1137;
	values[valuesById[1138] = "S2CFieldBossV2BuyTimes_ID"] = 1138;
	values[valuesById[1139] = "C2SFieldBossV2GetDamageLog_ID"] = 1139;
	values[valuesById[1140] = "S2CFieldBossV2GetDamageLog_ID"] = 1140;
	values[valuesById[1141] = "C2SFieldBossV2PlayerInBoss_ID"] = 1141;
	values[valuesById[1142] = "S2CFieldBossV2PlayerInBoss_ID"] = 1142;
	values[valuesById[1143] = "C2SGetMonthGift_ID"] = 1143;
	values[valuesById[1144] = "S2CGetMonthGift_ID"] = 1144;
	values[valuesById[1201] = "C2SGangInfo_ID"] = 1201;
	values[valuesById[1202] = "S2CGangInfo_ID"] = 1202;
	values[valuesById[1203] = "C2SGangList_ID"] = 1203;
	values[valuesById[1204] = "S2CGangList_ID"] = 1204;
	values[valuesById[1205] = "C2SCreateGang_ID"] = 1205;
	values[valuesById[1233] = "S2CCreateGang_ID"] = 1233;
	values[valuesById[1206] = "C2SApplyGang_ID"] = 1206;
	values[valuesById[1207] = "S2CApplyGang_ID"] = 1207;
	values[valuesById[1208] = "C2SAgreeApplyGang_ID"] = 1208;
	values[valuesById[1209] = "S2CAgreeApplyGang_ID"] = 1209;
	values[valuesById[1210] = "C2SUpNoticeGang_ID"] = 1210;
	values[valuesById[1211] = "S2CUpNoticeGang_ID"] = 1211;
	values[valuesById[1212] = "C2SReNameGang_ID"] = 1212;
	values[valuesById[1213] = "S2CReNameGang_ID"] = 1213;
	values[valuesById[1214] = "C2SGangMember_ID"] = 1214;
	values[valuesById[1215] = "S2CGangMember_ID"] = 1215;
	values[valuesById[1216] = "C2SGangApplys_ID"] = 1216;
	values[valuesById[1217] = "S2CGangApplys_ID"] = 1217;
	values[valuesById[1218] = "C2SUpGangFightVal_ID"] = 1218;
	values[valuesById[1219] = "S2CUpGangFightVal_ID"] = 1219;
	values[valuesById[1220] = "C2SGangKickMasterConsumes_ID"] = 1220;
	values[valuesById[1221] = "S2CGangKickMasterConsumes_ID"] = 1221;
	values[valuesById[1222] = "C2SUpGangMoney_ID"] = 1222;
	values[valuesById[1223] = "S2CUpGangMoney_ID"] = 1223;
	values[valuesById[1225] = "C2SGangRecruit_ID"] = 1225;
	values[valuesById[1226] = "S2CGangRecruit_ID"] = 1226;
	values[valuesById[1227] = "C2SGangUpDownDuty_ID"] = 1227;
	values[valuesById[1228] = "S2CGangUpDownDuty_ID"] = 1228;
	values[valuesById[1229] = "C2SGangKick_ID"] = 1229;
	values[valuesById[1230] = "S2CGangKick_ID"] = 1230;
	values[valuesById[1231] = "C2SGangKickMaster_ID"] = 1231;
	values[valuesById[1232] = "S2CGangKickMaster_ID"] = 1232;
	values[valuesById[1234] = "C2SGangGuardupLevel_ID"] = 1234;
	values[valuesById[1235] = "S2CGangGuardupLevel_ID"] = 1235;
	values[valuesById[1236] = "C2SGangExit_ID"] = 1236;
	values[valuesById[1237] = "S2CGangExit_ID"] = 1237;
	values[valuesById[1238] = "C2SGangEnterMap_ID"] = 1238;
	values[valuesById[1239] = "S2CGangEnterMap_ID"] = 1239;
	values[valuesById[1240] = "C2SGangLeaveMap_ID"] = 1240;
	values[valuesById[1241] = "S2CGangLeaveMap_ID"] = 1241;
	values[valuesById[1242] = "C2SGangRecord_ID"] = 1242;
	values[valuesById[1243] = "S2CGangRecord_ID"] = 1243;
	values[valuesById[1244] = "C2SGangGuardReward_ID"] = 1244;
	values[valuesById[1245] = "S2CGangGuardReward_ID"] = 1245;
	values[valuesById[1246] = "C2SGangFindReward_ID"] = 1246;
	values[valuesById[1247] = "S2CGangFindReward_ID"] = 1247;
	values[valuesById[1248] = "C2SGangUpSkill_ID"] = 1248;
	values[valuesById[1249] = "S2CGangUpSkill_ID"] = 1249;
	values[valuesById[1250] = "C2SGangFindSkill_ID"] = 1250;
	values[valuesById[1251] = "S2CGangFindSkill_ID"] = 1251;
	values[valuesById[1252] = "C2SGangFindExchange_ID"] = 1252;
	values[valuesById[1253] = "S2CGangFindExchange_ID"] = 1253;
	values[valuesById[1254] = "C2SGangExchange_ID"] = 1254;
	values[valuesById[1255] = "S2CGangExchange_ID"] = 1255;
	values[valuesById[1256] = "C2SGangRefreshExc_ID"] = 1256;
	values[valuesById[1257] = "S2CGangRefreshExc_ID"] = 1257;
	values[valuesById[1258] = "C2SGangFindPeach_ID"] = 1258;
	values[valuesById[1259] = "S2CGangFindPeach_ID"] = 1259;
	values[valuesById[1260] = "C2SGangEatPeach_ID"] = 1260;
	values[valuesById[1261] = "S2CGangEatPeach_ID"] = 1261;
	values[valuesById[1262] = "C2SGangPeachBox_ID"] = 1262;
	values[valuesById[1263] = "S2CGangPeachBox_ID"] = 1263;
	values[valuesById[1264] = "C2SGangAutoJoin_ID"] = 1264;
	values[valuesById[1265] = "S2CGangAutoJoin_ID"] = 1265;
	values[valuesById[1266] = "C2SGangMapOnekeyOver_ID"] = 1266;
	values[valuesById[1267] = "S2CGangMapOneKeyOver_ID"] = 1267;
	values[valuesById[1268] = "C2SGangMapReset_ID"] = 1268;
	values[valuesById[1269] = "S2CGangMapReset_ID"] = 1269;
	values[valuesById[1270] = "C2SRedPkgPool_ID"] = 1270;
	values[valuesById[1271] = "S2CRedPkgPool_ID"] = 1271;
	values[valuesById[1272] = "C2SRedPkgSend_ID"] = 1272;
	values[valuesById[1273] = "S2CRedPkgSend_ID"] = 1273;
	values[valuesById[1274] = "S2CRedPkgNotice_ID"] = 1274;
	values[valuesById[1275] = "C2SRedPkgRecieve_ID"] = 1275;
	values[valuesById[1276] = "S2CRedPkgRecieve_ID"] = 1276;
	values[valuesById[1277] = "C2SRedPkgSingle_ID"] = 1277;
	values[valuesById[1278] = "S2CRedPkgSingle_ID"] = 1278;
	values[valuesById[1279] = "C2SRedPkgPerRecord_ID"] = 1279;
	values[valuesById[1280] = "S2CRedPkgPerRecord_ID"] = 1280;
	values[valuesById[1281] = "C2SGangBossEnterMap_ID"] = 1281;
	values[valuesById[1282] = "S2CGangBossEnterMap_ID"] = 1282;
	values[valuesById[1283] = "C2SGangBossOverTime_ID"] = 1283;
	values[valuesById[1284] = "S2CGangBossOverTime_ID"] = 1284;
	values[valuesById[1285] = "S2CGangBossHurtSort_ID"] = 1285;
	values[valuesById[1286] = "C2SGangSimpleInfo_ID"] = 1286;
	values[valuesById[1287] = "S2CGangSimpleInfo_ID"] = 1287;
	values[valuesById[1288] = "S2CGangRedPot_ID"] = 1288;
	values[valuesById[1289] = "S2CGangBossRedPot_ID"] = 1289;
	values[valuesById[1290] = "C2SGangBossHurtSort_ID"] = 1290;
	values[valuesById[1291] = "K2SAddGangRedPkgMoney_ID"] = 1291;
	values[valuesById[1292] = "C2SGangBossKiller_ID"] = 1292;
	values[valuesById[1293] = "S2CGangBossKiller_ID"] = 1293;
	values[valuesById[1294] = "C2SGangMapResetInfo_ID"] = 1294;
	values[valuesById[1295] = "S2CGangMapResetInfo_ID"] = 1295;
	values[valuesById[1296] = "S2KGangSimpleInfo_ID"] = 1296;
	values[valuesById[1297] = "C2SRedPckShieldLog_ID"] = 1297;
	values[valuesById[1298] = "S2CRedPckShieldLog_ID"] = 1298;
	values[valuesById[1299] = "S2CGangBossFiveHurt_ID"] = 1299;
	values[valuesById[1300] = "C2SGangCollect_ID"] = 1300;
	values[valuesById[1301] = "S2CGangCollect_ID"] = 1301;
	values[valuesById[1351] = "C2SIntoSoulGradeUp_ID"] = 1351;
	values[valuesById[1352] = "S2CIntoSoulGradeUp_ID"] = 1352;
	values[valuesById[1353] = "C2SGodEquipAwake_ID"] = 1353;
	values[valuesById[1354] = "S2CGodEquipAwake_ID"] = 1354;
	values[valuesById[1355] = "C2SGodForge_ID"] = 1355;
	values[valuesById[1356] = "S2CGodForge_ID"] = 1356;
	values[valuesById[1357] = "C2SGodForgeSave_ID"] = 1357;
	values[valuesById[1358] = "S2CGodForgeSave_ID"] = 1358;
	values[valuesById[1359] = "C2SGodEquipMelt_ID"] = 1359;
	values[valuesById[1360] = "S2CGodEquipMelt_ID"] = 1360;
	values[valuesById[1401] = "C2SCreatePrecious_ID"] = 1401;
	values[valuesById[1402] = "S2CCreatePrecious_ID"] = 1402;
	values[valuesById[1403] = "C2SMeltPrecious_ID"] = 1403;
	values[valuesById[1404] = "S2CMeltPrecious_ID"] = 1404;
	values[valuesById[1405] = "C2SWearPrecious_ID"] = 1405;
	values[valuesById[1406] = "S2CWearPrecious_ID"] = 1406;
	values[valuesById[1407] = "C2SLockPrecious_ID"] = 1407;
	values[valuesById[1408] = "S2CLockPrecious_ID"] = 1408;
	values[valuesById[1409] = "C2SGetPreciousPos_ID"] = 1409;
	values[valuesById[1410] = "S2CGetPreciousPos_ID"] = 1410;
	values[valuesById[1411] = "C2SPreciousForge_ID"] = 1411;
	values[valuesById[1412] = "S2CPreciousForge_ID"] = 1412;
	values[valuesById[1413] = "C2SPreciousEat_ID"] = 1413;
	values[valuesById[1414] = "S2CPreciousEat_ID"] = 1414;
	values[valuesById[1415] = "C2SPreciousSoul_ID"] = 1415;
	values[valuesById[1416] = "S2CPreciousSoul_ID"] = 1416;
	values[valuesById[1417] = "C2SPreciousSoulUp_ID"] = 1417;
	values[valuesById[1418] = "S2CPreciousSoulUp_ID"] = 1418;
	values[valuesById[1419] = "C2SPreciousGive_ID"] = 1419;
	values[valuesById[1420] = "S2CPreciousGive_ID"] = 1420;
	values[valuesById[1421] = "C2SCreatePreciousFast_ID"] = 1421;
	values[valuesById[1422] = "S2CCreatePreciousFast_ID"] = 1422;
	values[valuesById[1423] = "C2SPreciousRobot_ID"] = 1423;
	values[valuesById[1424] = "S2CPreciousRobot_ID"] = 1424;
	values[valuesById[1501] = "C2SReviveLife_ID"] = 1501;
	values[valuesById[1502] = "S2CReviveLife_ID"] = 1502;
	values[valuesById[1503] = "S2CGangActWarGangPCount_ID"] = 1503;
	values[valuesById[1504] = "S2CGangActWarGangLocal_ID"] = 1504;
	values[valuesById[1505] = "C2SActiveNineDayReviceExchange_ID"] = 1505;
	values[valuesById[1506] = "S2CActiveNineDayReviceExchange_ID"] = 1506;
	values[valuesById[1507] = "C2SJoinActive_ID"] = 1507;
	values[valuesById[1508] = "S2CJoinActive_ID"] = 1508;
	values[valuesById[1509] = "C2SLeaveActive_ID"] = 1509;
	values[valuesById[1510] = "S2CLeaveActive_ID"] = 1510;
	values[valuesById[1511] = "S2CGangWarDragonPosition_ID"] = 1511;
	values[valuesById[1512] = "GangHurtInfo_ID"] = 1512;
	values[valuesById[1513] = "C2SActGangWarScoreFind_ID"] = 1513;
	values[valuesById[1514] = "S2CActGangWarScoreFind_ID"] = 1514;
	values[valuesById[1515] = "C2SActTeamRecruit_ID"] = 1515;
	values[valuesById[1516] = "S2CActTeamRecruit_ID"] = 1516;
	values[valuesById[1517] = "K2SJoinFuncActiveHandleEvent_ID"] = 1517;
	values[valuesById[1526] = "S2CGangWarGangSort_ID"] = 1526;
	values[valuesById[1527] = "C2SActCreateTeam_ID"] = 1527;
	values[valuesById[1528] = "S2CActCreateTeam_ID"] = 1528;
	values[valuesById[1529] = "C2SActFindTeams_ID"] = 1529;
	values[valuesById[1530] = "S2CActFindTeams_ID"] = 1530;
	values[valuesById[1531] = "C2SActJoinTeam_ID"] = 1531;
	values[valuesById[1532] = "S2CActJoinTeam_ID"] = 1532;
	values[valuesById[1533] = "C2SActFindTeam_ID"] = 1533;
	values[valuesById[1534] = "S2CActFindTeam_ID"] = 1534;
	values[valuesById[1535] = "ActTeamInfo_ID"] = 1535;
	values[valuesById[1536] = "ActTeamMemInfo_ID"] = 1536;
	values[valuesById[1537] = "C2SActNeedFightVal_ID"] = 1537;
	values[valuesById[1538] = "S2CActNeedFightVal_ID"] = 1538;
	values[valuesById[1539] = "S2CActGiveUpDragon_ID"] = 1539;
	values[valuesById[1540] = "C2SActGiveUpDragon_ID"] = 1540;
	values[valuesById[1541] = "S2CActGangWarInDragonGangs_ID"] = 1541;
	values[valuesById[1543] = "C2SActGangWarSwitchMap_ID"] = 1543;
	values[valuesById[1544] = "S2CActGangWarSwitchMap_ID"] = 1544;
	values[valuesById[1545] = "C2SActGangWarScoreExchange_ID"] = 1545;
	values[valuesById[1546] = "S2CActGangWarScoreExchange_ID"] = 1546;
	values[valuesById[1548] = "C2SActGangWarPersonRank_ID"] = 1548;
	values[valuesById[1549] = "S2CActGangWarPersonRank_ID"] = 1549;
	values[valuesById[1550] = "ActGangWarPersonRank_ID"] = 1550;
	values[valuesById[1551] = "C2SActGangWarGangRank_ID"] = 1551;
	values[valuesById[1552] = "S2CActGangWarGangRank_ID"] = 1552;
	values[valuesById[1553] = "ActGangWarGangRank_ID"] = 1553;
	values[valuesById[1554] = "C2SActGangWarKillRank_ID"] = 1554;
	values[valuesById[1555] = "S2CActGangWarKillRank_ID"] = 1555;
	values[valuesById[1556] = "ActGangWarKillRank_ID"] = 1556;
	values[valuesById[1557] = "C2SActGangWarScoreRank_ID"] = 1557;
	values[valuesById[1558] = "S2CActGangWarScoreRank_ID"] = 1558;
	values[valuesById[1559] = "ActGangWarScoreRank_ID"] = 1559;
	values[valuesById[1561] = "S2CActStart_ID"] = 1561;
	values[valuesById[1562] = "C2SActiveNineDayFindExchange_ID"] = 1562;
	values[valuesById[1563] = "S2CActiveNineDayFindExchange_ID"] = 1563;
	values[valuesById[1564] = "S2CActiveNineDayScoreChange_ID"] = 1564;
	values[valuesById[1565] = "S2CActOver_ID"] = 1565;
	values[valuesById[1566] = "S2CActIcon_ID"] = 1566;
	values[valuesById[1567] = "C2SActGangWarPreInfo_ID"] = 1567;
	values[valuesById[1568] = "S2CActGangWarPreInfo_ID"] = 1568;
	values[valuesById[1569] = "C2SActNineDayPreInfo_ID"] = 1569;
	values[valuesById[1570] = "S2CActNineDayPreInfo_ID"] = 1570;
	values[valuesById[1571] = "K2SActGangWarPreInfo_ID"] = 1571;
	values[valuesById[1572] = "K2SActNineDayPreInfo_ID"] = 1572;
	values[valuesById[1573] = "S2CSouthGateStatus_ID"] = 1573;
	values[valuesById[1574] = "C2SActSountGateFight_ID"] = 1574;
	values[valuesById[1575] = "S2CActSountGateFight_ID"] = 1575;
	values[valuesById[1576] = "C2SActExitTeam_ID"] = 1576;
	values[valuesById[1577] = "S2CActExitTeam_ID"] = 1577;
	values[valuesById[1579] = "S2CNiceDayClose_ID"] = 1579;
	values[valuesById[1580] = "S2CActiveGangWarScoreChange_ID"] = 1580;
	values[valuesById[1581] = "C2SActNineDaySort_ID"] = 1581;
	values[valuesById[1582] = "S2CActNineDaySort_ID"] = 1582;
	values[valuesById[1583] = "S2CGangWarBeforeCD_ID"] = 1583;
	values[valuesById[1584] = "S2CActGangWarSettlement_ID"] = 1584;
	values[valuesById[1585] = "S2CActiveGangWarBeforeInsideScore_ID"] = 1585;
	values[valuesById[1601] = "C2SGetSendShopList_ID"] = 1601;
	values[valuesById[1602] = "S2CGetSendShopList_ID"] = 1602;
	values[valuesById[1603] = "C2SGetSendShopById_ID"] = 1603;
	values[valuesById[1604] = "S2CGetSendShopById_ID"] = 1604;
	values[valuesById[1605] = "C2SSendShopBuy_ID"] = 1605;
	values[valuesById[1606] = "S2CSendShopBuy_ID"] = 1606;
	values[valuesById[1607] = "C2SSendShopClear_ID"] = 1607;
	values[valuesById[1608] = "S2CSendShopClear_ID"] = 1608;
	values[valuesById[1609] = "C2SSendShopOnSale_ID"] = 1609;
	values[valuesById[1610] = "S2CSendShopOnSale_ID"] = 1610;
	values[valuesById[1611] = "C2SGetMySendShop_ID"] = 1611;
	values[valuesById[1612] = "S2CGetMySendShop_ID"] = 1612;
	values[valuesById[1613] = "C2SSaleLog_ID"] = 1613;
	values[valuesById[1614] = "S2CSaleLog_ID"] = 1614;
	values[valuesById[1615] = "C2SMarkSendShop_ID"] = 1615;
	values[valuesById[1616] = "S2CMarkSendShop_ID"] = 1616;
	values[valuesById[1617] = "S2KGetSendShopList_ID"] = 1617;
	values[valuesById[1618] = "S2KGetMySendShopList_ID"] = 1618;
	values[valuesById[1619] = "S2KSendShopPreBuy_ID"] = 1619;
	values[valuesById[1620] = "K2SSendShopPreBuy_ID"] = 1620;
	values[valuesById[1621] = "S2KSendShopBuy_ID"] = 1621;
	values[valuesById[1623] = "S2KSendShopClear_ID"] = 1623;
	values[valuesById[1624] = "K2SSendShopClear_ID"] = 1624;
	values[valuesById[1625] = "S2KSendShopOnSale_ID"] = 1625;
	values[valuesById[1626] = "S2KGetSaleLog_ID"] = 1626;
	values[valuesById[1627] = "C2SGetSendCfg_ID"] = 1627;
	values[valuesById[1628] = "S2CGetSendCfg_ID"] = 1628;
	values[valuesById[1629] = "C2SSendItem_ID"] = 1629;
	values[valuesById[1630] = "S2CSendItem_ID"] = 1630;
	values[valuesById[1631] = "C2SGetSendUserInfo_ID"] = 1631;
	values[valuesById[1632] = "S2CGetSendUserInfo_ID"] = 1632;
	values[valuesById[1633] = "K2SSendShopBuy_ID"] = 1633;
	values[valuesById[1701] = "C2SGetDragonLog_ID"] = 1701;
	values[valuesById[1702] = "S2CGetDragonLog_ID"] = 1702;
	values[valuesById[1703] = "C2SStartFightDragon_ID"] = 1703;
	values[valuesById[1704] = "S2CStartFightDragon_ID"] = 1704;
	values[valuesById[1801] = "C2SShowItem_ID"] = 1801;
	values[valuesById[1802] = "S2CShowItem_ID"] = 1802;
	values[valuesById[1803] = "S2CReportShowItem_ID"] = 1803;
	values[valuesById[1804] = "C2SGetShowInfo_ID"] = 1804;
	values[valuesById[1805] = "S2CGetShowInfo_ID"] = 1805;
	values[valuesById[1806] = "S2KShowItem_ID"] = 1806;
	values[valuesById[1901] = "S2C81Info_ID"] = 1901;
	values[valuesById[1902] = "C2S81Sweep_ID"] = 1902;
	values[valuesById[1903] = "S2C81Sweep_ID"] = 1903;
	values[valuesById[1904] = "C2S81BuyBox_ID"] = 1904;
	values[valuesById[1905] = "S2C81BuyBox_ID"] = 1905;
	values[valuesById[1906] = "C2SViewInstance81Data_ID"] = 1906;
	values[valuesById[1907] = "S2CViewInstance81Data_ID"] = 1907;
	values[valuesById[2001] = "C2SExpeditionEnemy_ID"] = 2001;
	values[valuesById[2002] = "S2CExpeditionEnemy_ID"] = 2002;
	values[valuesById[2003] = "C2SExpeditionTeam_ID"] = 2003;
	values[valuesById[2004] = "S2CExpeditionTeam_ID"] = 2004;
	values[valuesById[2005] = "S2CExpeditionInfo_ID"] = 2005;
	values[valuesById[2006] = "C2SExpeditionStartFight_ID"] = 2006;
	values[valuesById[2007] = "S2CExpeditionStartFight_ID"] = 2007;
	values[valuesById[2008] = "C2SExpeditionGetPrize_ID"] = 2008;
	values[valuesById[2009] = "S2CExpeditionGetPrize_ID"] = 2009;
	values[valuesById[2010] = "C2SExpeditionRelive_ID"] = 2010;
	values[valuesById[2011] = "S2CExpeditionRelive_ID"] = 2011;
	values[valuesById[2012] = "C2SExpeditionSaveTeam_ID"] = 2012;
	values[valuesById[2013] = "S2CExpeditionSaveTeam_ID"] = 2013;
	values[valuesById[2014] = "C2SExpeditionReset_ID"] = 2014;
	values[valuesById[2015] = "S2CExpeditionReset_ID"] = 2015;
	values[valuesById[2016] = "C2SExpeditionRefreshBuff_ID"] = 2016;
	values[valuesById[2017] = "S2CExpeditionRefreshBuff_ID"] = 2017;
	values[valuesById[2018] = "C2SExpeditionSweep_ID"] = 2018;
	values[valuesById[2019] = "S2CExpeditionSweep_ID"] = 2019;
	values[valuesById[2101] = "C2SGodClubSignUp_ID"] = 2101;
	values[valuesById[2102] = "S2CGodClubSignUp_ID"] = 2102;
	values[valuesById[2103] = "C2SGodClubFight_ID"] = 2103;
	values[valuesById[2104] = "S2CGodClubFight_ID"] = 2104;
	values[valuesById[2105] = "C2SGodClubFightReport_ID"] = 2105;
	values[valuesById[2106] = "S2CGodClubFightReport_ID"] = 2106;
	values[valuesById[2107] = "C2SGodClub16_ID"] = 2107;
	values[valuesById[2108] = "S2CGodClub16_ID"] = 2108;
	values[valuesById[2109] = "C2SGodClubStakeInfo_ID"] = 2109;
	values[valuesById[2110] = "S2CGodClubStakeInfo_ID"] = 2110;
	values[valuesById[2111] = "C2SGodClubStake_ID"] = 2111;
	values[valuesById[2112] = "S2CGodClubStake_ID"] = 2112;
	values[valuesById[2201] = "S2CPassCheckInfo_ID"] = 2201;
	values[valuesById[2202] = "C2SPassCheckGetPrize_ID"] = 2202;
	values[valuesById[2203] = "S2CPassCheckGetPrize_ID"] = 2203;
	values[valuesById[2204] = "C2SPassCheckFast_ID"] = 2204;
	values[valuesById[2205] = "S2CPassCheckFast_ID"] = 2205;
	values[valuesById[3001] = "C2SGetZF_ID"] = 3001;
	values[valuesById[3002] = "S2CGetZF_ID"] = 3002;
	values[valuesById[3003] = "C2SZFPetUp_ID"] = 3003;
	values[valuesById[3004] = "S2CZFPetUp_ID"] = 3004;
	values[valuesById[3005] = "C2SZFState_ID"] = 3005;
	values[valuesById[3006] = "S2CZFState_ID"] = 3006;
	values[valuesById[3101] = "C2SFuncOpen_ID"] = 3101;
	values[valuesById[3102] = "S2CFuncOpen_ID"] = 3102;
	values[valuesById[3103] = "C2SRobotZF_ID"] = 3103;
	values[valuesById[3104] = "C2SZFUnlock_ID"] = 3104;
	values[valuesById[3105] = "S2CZFUnlock_ID"] = 3105;
	values[valuesById[4101] = "S2CUserPhotoBook_ID"] = 4101;
	values[valuesById[4102] = "C2SPhotoBookLevelUp_ID"] = 4102;
	values[valuesById[4103] = "S2CPhotoBookLevelUp_ID"] = 4103;
	values[valuesById[4104] = "C2SPhotoBookDel_ID"] = 4104;
	values[valuesById[4105] = "S2CPhotoBookDel_ID"] = 4105;
	values[valuesById[8001] = "S2KLoginSuccess_ID"] = 8001;
	values[valuesById[8002] = "K2SBattlefieldReport_ID"] = 8002;
	values[valuesById[8003] = "K2SEnterMap_ID"] = 8003;
	values[valuesById[8004] = "S2KEnterMap_ID"] = 8004;
	values[valuesById[8005] = "S2KLeaveMap_ID"] = 8005;
	values[valuesById[8006] = "S2KLeaveInstanceTeam_ID"] = 8006;
	values[valuesById[8007] = "K2SAddItem_ID"] = 8007;
	values[valuesById[8008] = "K2SChangeAttr_ID"] = 8008;
	values[valuesById[8009] = "S2KLogout_ID"] = 8009;
	values[valuesById[8011] = "S2KPlayerData_ID"] = 8011;
	values[valuesById[8012] = "K2SUnlockSkin_ID"] = 8012;
	values[valuesById[8013] = "K2SDeleteSkin_ID"] = 8013;
	values[valuesById[8014] = "K2SMail_ID"] = 8014;
	values[valuesById[8015] = "K2SActState_ID"] = 8015;
	values[valuesById[8016] = "K2SBroadcastGang_ID"] = 8016;
	values[valuesById[8017] = "S2KReviveLife_ID"] = 8017;
	values[valuesById[8018] = "S2KWorldLevel_ID"] = 8018;
	values[valuesById[8019] = "K2SAddRedPkg_ID"] = 8019;
	values[valuesById[8101] = "C2SGetTeamList_ID"] = 8101;
	values[valuesById[8102] = "S2CGetTeamList_ID"] = 8102;
	values[valuesById[8103] = "C2SGetMemberList_ID"] = 8103;
	values[valuesById[8104] = "S2CGetMemberList_ID"] = 8104;
	values[valuesById[8105] = "S2KCrossChat_ID"] = 8105;
	values[valuesById[8106] = "K2SCrossChat_ID"] = 8106;
	values[valuesById[8107] = "K2SAddTmpTitle_ID"] = 8107;
	values[valuesById[8108] = "C2SJoinInstance_ID"] = 8108;
	values[valuesById[8109] = "S2CJoinInstance_ID"] = 8109;
	values[valuesById[8110] = "C2SLeaveInstance_ID"] = 8110;
	values[valuesById[8111] = "S2CLeaveInstance_ID"] = 8111;
	values[valuesById[8112] = "C2SKillInstance_ID"] = 8112;
	values[valuesById[8113] = "S2CKillInstance_ID"] = 8113;
	values[valuesById[8114] = "C2SStartInstance_ID"] = 8114;
	values[valuesById[8115] = "S2CStartInstance_ID"] = 8115;
	values[valuesById[8118] = "C2SLeaveInstanceCopy_ID"] = 8118;
	values[valuesById[8119] = "S2CLeaveInstanceCopy_ID"] = 8119;
	values[valuesById[8120] = "S2KCreateServerIds_ID"] = 8120;
	values[valuesById[8121] = "K2SGetPlayerByLevel_ID"] = 8121;
	values[valuesById[8122] = "S2KGetPlayerOtherInfo_ID"] = 8122;
	values[valuesById[9996] = "S2SServerInfo_ID"] = 9996;
	values[valuesById[9997] = "S2SServerPlayerHandle_ID"] = 9997;
	values[valuesById[9998] = "S2SResponseMsg_ID"] = 9998;
	values[valuesById[9999] = "S2SRequestMsg_ID"] = 9999;
	values[valuesById[10000] = "S2SServerPlayerPck_ID"] = 10000;
	values[valuesById[8201] = "C2SGoldTree_ID"] = 8201;
	values[valuesById[8202] = "S2CGoldTree_ID"] = 8202;
	values[valuesById[8203] = "C2SGetGoldTreeInfo_ID"] = 8203;
	values[valuesById[8204] = "S2CGetGoldTreeInfo_ID"] = 8204;
	values[valuesById[8300] = "S2CAllActivity_ID"] = 8300;
	values[valuesById[8301] = "S2CActivityStart_ID"] = 8301;
	values[valuesById[8302] = "S2CActivityEnd_ID"] = 8302;
	values[valuesById[8303] = "S2CActivityInit_ID"] = 8303;
	values[valuesById[8304] = "S2CActivityRedPoint_ID"] = 8304;
	values[valuesById[8305] = "S2CActivityData_ID"] = 8305;
	values[valuesById[8306] = "S2CActivityIcon_ID"] = 8306;
	values[valuesById[8307] = "S2CContinueRechargeTaskNum_ID"] = 8307;
	values[valuesById[11001] = "C2SBuyDrawItem_ID"] = 11001;
	values[valuesById[11002] = "S2CBuyDrawItem_ID"] = 11002;
	values[valuesById[11003] = "C2SDraw_ID"] = 11003;
	values[valuesById[11004] = "S2CDraw_ID"] = 11004;
	values[valuesById[11005] = "C2SGetDrawLog_ID"] = 11005;
	values[valuesById[11006] = "S2CGetDrawLog_ID"] = 11006;
	values[valuesById[11007] = "C2SGetDrawData_ID"] = 11007;
	values[valuesById[11008] = "S2CGetDrawData_ID"] = 11008;
	values[valuesById[11009] = "C2SPlayerDrawData_ID"] = 11009;
	values[valuesById[11010] = "S2CPlayerDrawData_ID"] = 11010;
	values[valuesById[11011] = "C2SGetDrawListRate_ID"] = 11011;
	values[valuesById[11012] = "S2CGetDrawListRate_ID"] = 11012;
	values[valuesById[11150] = "C2SGetChargeReturnData_ID"] = 11150;
	values[valuesById[11151] = "S2CGetChargeReturnData_ID"] = 11151;
	values[valuesById[11152] = "C2SPlayerChargeReturnData_ID"] = 11152;
	values[valuesById[11153] = "S2CPlayerChargeReturnData_ID"] = 11153;
	values[valuesById[11154] = "C2SChargeReturn_ID"] = 11154;
	values[valuesById[11155] = "S2CChargeReturn_ID"] = 11155;
	values[valuesById[11156] = "C2SGetChargeReturnLog_ID"] = 11156;
	values[valuesById[11157] = "S2CGetChargeReturnLog_ID"] = 11157;
	values[valuesById[11158] = "C2SChargeReturnReceivePrize_ID"] = 11158;
	values[valuesById[11159] = "S2CChargeReturnReceivePrize_ID"] = 11159;
	values[valuesById[11101] = "C2SGetActBossInfo_ID"] = 11101;
	values[valuesById[11102] = "S2CGetActBossInfo_ID"] = 11102;
	values[valuesById[11103] = "C2SFightActBoss_ID"] = 11103;
	values[valuesById[11104] = "S2CFightActBoss_ID"] = 11104;
	values[valuesById[11105] = "C2SGetActBossPrize_ID"] = 11105;
	values[valuesById[11106] = "S2CGetActBossPrize_ID"] = 11106;
	values[valuesById[11107] = "C2SPlayerBossInfo_ID"] = 11107;
	values[valuesById[11108] = "S2CPlayerBossInfo_ID"] = 11108;
	values[valuesById[11201] = "C2SGetActDareBossInfo_ID"] = 11201;
	values[valuesById[11202] = "S2CGetActDareBossInfo_ID"] = 11202;
	values[valuesById[11203] = "C2SFightActDareBoss_ID"] = 11203;
	values[valuesById[11204] = "S2CFightActDareBoss_ID"] = 11204;
	values[valuesById[11205] = "C2SGetActDareBossPrize_ID"] = 11205;
	values[valuesById[11206] = "S2CGetActDareBossPrize_ID"] = 11206;
	values[valuesById[11207] = "C2SPlayerDareBossInfo_ID"] = 11207;
	values[valuesById[11208] = "S2CPlayerDareBossInfo_ID"] = 11208;
	values[valuesById[11209] = "C2SPlayerDareState_ID"] = 11209;
	values[valuesById[11210] = "S2CPlayerDareState_ID"] = 11210;
	values[valuesById[11211] = "C2SPlayerDareIsOpen_ID"] = 11211;
	values[valuesById[11212] = "S2CPlayerDareIsOpen_ID"] = 11212;
	values[valuesById[12001] = "C2SGetActShopList_ID"] = 12001;
	values[valuesById[12002] = "S2CGetActShopList_ID"] = 12002;
	values[valuesById[12003] = "C2SActShopBuy_ID"] = 12003;
	values[valuesById[12004] = "S2CActShopBuy_ID"] = 12004;
	values[valuesById[12005] = "C2SGetActGiftList_ID"] = 12005;
	values[valuesById[12006] = "S2CGetActGiftList_ID"] = 12006;
	values[valuesById[12007] = "C2SActGiftBuy_ID"] = 12007;
	values[valuesById[12008] = "S2CActGiftBuy_ID"] = 12008;
	values[valuesById[12009] = "C2SActGetPrize_ID"] = 12009;
	values[valuesById[12010] = "S2CActGetPrize_ID"] = 12010;
	values[valuesById[12101] = "C2SGetChargeMallList_ID"] = 12101;
	values[valuesById[12102] = "S2CGetChargeMallList_ID"] = 12102;
	values[valuesById[12103] = "C2SChargeMallBuy_ID"] = 12103;
	values[valuesById[12104] = "S2CChargeMallBuy_ID"] = 12104;
	values[valuesById[12105] = "S2CPayNotify_ID"] = 12105;
	values[valuesById[12106] = "S2CVipService_ID"] = 12106;
	values[valuesById[12151] = "C2SGetActTask_ID"] = 12151;
	values[valuesById[12152] = "S2CGetActTask_ID"] = 12152;
	values[valuesById[12153] = "S2CGetFirstRechargeTask_ID"] = 12153;
	values[valuesById[12161] = "C2SGetGodTowerActTaskDay_ID"] = 12161;
	values[valuesById[12162] = "S2CGetGodTowerActTaskDay_ID"] = 12162;
	values[valuesById[12201] = "C2SGetInvestInfo_ID"] = 12201;
	values[valuesById[12202] = "S2CGetInvestInfo_ID"] = 12202;
	values[valuesById[12203] = "C2SBuyInvest_ID"] = 12203;
	values[valuesById[12204] = "S2CBuyInvest_ID"] = 12204;
	values[valuesById[12205] = "C2SPlayerInvestData_ID"] = 12205;
	values[valuesById[12206] = "S2CPlayerInvestData_ID"] = 12206;
	values[valuesById[12301] = "C2SGetActRankInfo_ID"] = 12301;
	values[valuesById[12302] = "S2CGetActRankInfo_ID"] = 12302;
	values[valuesById[12303] = "C2SGetActRankData_ID"] = 12303;
	values[valuesById[12304] = "S2CGetActRankData_ID"] = 12304;
	values[valuesById[12350] = "C2SGetActPicture_ID"] = 12350;
	values[valuesById[12351] = "S2CGetActPicture_ID"] = 12351;
	values[valuesById[12352] = "C2SActPictureLight_ID"] = 12352;
	values[valuesById[12353] = "S2CActPictureLight_ID"] = 12353;
	values[valuesById[12401] = "C2SRedeemCode_ID"] = 12401;
	values[valuesById[12402] = "S2CRedeemCode_ID"] = 12402;
	values[valuesById[12501] = "C2SActRecoveryData_ID"] = 12501;
	values[valuesById[12502] = "S2CActRecoveryData_ID"] = 12502;
	values[valuesById[12503] = "C2SActRecoveryEquip_ID"] = 12503;
	values[valuesById[12504] = "S2CActRecoveryEquip_ID"] = 12504;
	values[valuesById[12601] = "C2SGetActGiftOnline_ID"] = 12601;
	values[valuesById[12602] = "S2CGetActGiftOnline_ID"] = 12602;
	values[valuesById[12701] = "C2SGetActGiftMiracle_ID"] = 12701;
	values[valuesById[12702] = "S2CGetActGiftMiracle_ID"] = 12702;
	values[valuesById[12801] = "C2SGetActGiftLimit_ID"] = 12801;
	values[valuesById[12802] = "S2CGetActGiftLimit_ID"] = 12802;
	values[valuesById[12901] = "C2SGetActGiftXRmbInfo_ID"] = 12901;
	values[valuesById[12902] = "S2CGetActGiftXRmbInfo_ID"] = 12902;
	values[valuesById[12911] = "C2SGetActCollectFontInfo_ID"] = 12911;
	values[valuesById[12912] = "S2CGetActCollectFontInfo_ID"] = 12912;
	values[valuesById[12921] = "C2SGetActLifeVipGiveInfo_ID"] = 12921;
	values[valuesById[12922] = "S2CGetActLifeVipGiveInfo_ID"] = 12922;
	values[valuesById[13000] = "S2CAllGodItem_ID"] = 13000;
	values[valuesById[13001] = "C2SUnlockGodItem_ID"] = 13001;
	values[valuesById[13002] = "S2CUnlockGodItem_ID"] = 13002;
	values[valuesById[13003] = "C2SGodItemLevelUp_ID"] = 13003;
	values[valuesById[13004] = "S2CGodItemLevelUp_ID"] = 13004;
	values[valuesById[13005] = "C2SGodItemSoulUp_ID"] = 13005;
	values[valuesById[13006] = "S2CGodItemSoulUp_ID"] = 13006;
	values[valuesById[13007] = "C2SGodItemSoul2Up_ID"] = 13007;
	values[valuesById[13008] = "S2CGodItemSoul2Up_ID"] = 13008;
	values[valuesById[13009] = "C2SGodItemSoul2SkillUp_ID"] = 13009;
	values[valuesById[13010] = "S2CGodItemSoul2SkillUp_ID"] = 13010;
	values[valuesById[13011] = "C2SGodItemSoul3Up_ID"] = 13011;
	values[valuesById[13012] = "S2CGodItemSoul3Up_ID"] = 13012;
	values[valuesById[13013] = "C2SGodItemForge_ID"] = 13013;
	values[valuesById[13014] = "S2CGodItemForge_ID"] = 13014;
	values[valuesById[13015] = "C2SGodItemForgeSave_ID"] = 13015;
	values[valuesById[13016] = "S2CGodItemForgeSave_ID"] = 13016;
	values[valuesById[13017] = "C2SGodItemSoul4Up_ID"] = 13017;
	values[valuesById[13018] = "S2CGodItemSoul4Up_ID"] = 13018;
	values[valuesById[13019] = "C2SGodItemStoneUp_ID"] = 13019;
	values[valuesById[13020] = "S2CGodItemStoneUp_ID"] = 13020;
	values[valuesById[13021] = "C2SGodItemStoneCut_ID"] = 13021;
	values[valuesById[13022] = "S2CGodItemStoneCut_ID"] = 13022;
	values[valuesById[14000] = "C2SKingState_ID"] = 14000;
	values[valuesById[14001] = "S2CKingState_ID"] = 14001;
	values[valuesById[14002] = "C2SKingMatch_ID"] = 14002;
	values[valuesById[14003] = "S2CKingMatch_ID"] = 14003;
	values[valuesById[14004] = "C2SKingFight_ID"] = 14004;
	values[valuesById[14005] = "S2CKingFight_ID"] = 14005;
	values[valuesById[14006] = "C2SKingRank_ID"] = 14006;
	values[valuesById[14007] = "S2CKingRank_ID"] = 14007;
	values[valuesById[14010] = "C2SKingPlayer_ID"] = 14010;
	values[valuesById[14011] = "S2CKingPlayer_ID"] = 14011;
	values[valuesById[14012] = "C2SKingBuyTimes_ID"] = 14012;
	values[valuesById[14013] = "S2CKingBuyTimes_ID"] = 14013;
	values[valuesById[14014] = "C2SKingInfo_ID"] = 14014;
	values[valuesById[14015] = "S2CKingInfo_ID"] = 14015;
	values[valuesById[14016] = "C2SKingRespect_ID"] = 14016;
	values[valuesById[14017] = "S2CKingRespect_ID"] = 14017;
	values[valuesById[14018] = "C2SKingGetBuyInfo_ID"] = 14018;
	values[valuesById[14019] = "S2CKingGetBuyInfo_ID"] = 14019;
	values[valuesById[15001] = "S2CWorldBossRank5_ID"] = 15001;
	values[valuesById[15002] = "S2CActWorldBossSettlement_ID"] = 15002;
	values[valuesById[15003] = "K2SWorldBossKillData_ID"] = 15003;
	values[valuesById[15004] = "C2SGetWorldBossKillData_ID"] = 15004;
	values[valuesById[15006] = "S2CGetWorldBossKillData_ID"] = 15006;
	values[valuesById[15007] = "S2CBossRefreshTime_ID"] = 15007;
	values[valuesById[15008] = "C2SGetWorldBossRank_ID"] = 15008;
	values[valuesById[15009] = "S2CGetWorldBossRank_ID"] = 15009;
	values[valuesById[16001] = "C2SWordsWear_ID"] = 16001;
	values[valuesById[16002] = "S2CWordsWear_ID"] = 16002;
	values[valuesById[16003] = "C2SWordsStick_ID"] = 16003;
	values[valuesById[16004] = "S2CWordsStick_ID"] = 16004;
	values[valuesById[17001] = "C2SGodHerbEnter_ID"] = 17001;
	values[valuesById[17002] = "S2CGodHerbEnter_ID"] = 17002;
	values[valuesById[17003] = "C2SGodHerbRefresh_ID"] = 17003;
	values[valuesById[17004] = "S2CGodHerbRefresh_ID"] = 17004;
	values[valuesById[17005] = "C2SGodHerbCollect_ID"] = 17005;
	values[valuesById[17006] = "S2CGodHerbCollect_ID"] = 17006;
	values[valuesById[17007] = "C2SGetGodHerbLog_ID"] = 17007;
	values[valuesById[17008] = "S2CGetGodHerbLog_ID"] = 17008;
	values[valuesById[17101] = "S2CRobberyInfo_ID"] = 17101;
	values[valuesById[17102] = "C2SGetRobberyData_ID"] = 17102;
	values[valuesById[17103] = "S2CGetRobberyData_ID"] = 17103;
	values[valuesById[17104] = "C2SRobbery_ID"] = 17104;
	values[valuesById[17105] = "S2CRobbery_ID"] = 17105;
	values[valuesById[17106] = "C2SRobberyLuck_ID"] = 17106;
	values[valuesById[17107] = "S2CRobberyLuck_ID"] = 17107;
	values[valuesById[17108] = "C2SGetRobberyLog_ID"] = 17108;
	values[valuesById[17109] = "S2CGetRobberyLog_ID"] = 17109;
	values[valuesById[17202] = "C2SDevilSoulPos_ID"] = 17202;
	values[valuesById[17203] = "S2CDevilSoulPos_ID"] = 17203;
	values[valuesById[17204] = "C2SDevilSoulLevelUp_ID"] = 17204;
	values[valuesById[17205] = "S2CDevilSoulLevelUp_ID"] = 17205;
	values[valuesById[17206] = "C2SDevilSoulStarUp_ID"] = 17206;
	values[valuesById[17207] = "S2CDevilSoulStarUp_ID"] = 17207;
	values[valuesById[17208] = "C2SDevilSoulDel_ID"] = 17208;
	values[valuesById[17209] = "S2CDevilSoulDel_ID"] = 17209;
	values[valuesById[17210] = "C2SDevilSoulAuto_ID"] = 17210;
	values[valuesById[17211] = "S2CDevilSoulAuto_ID"] = 17211;
	values[valuesById[17300] = "C2SBuyChargeGift_ID"] = 17300;
	values[valuesById[17301] = "S2CBuyChargeGift_ID"] = 17301;
	values[valuesById[17302] = "S2CAllChargeGift_ID"] = 17302;
	values[valuesById[17403] = "C2SGetStateDemons_ID"] = 17403;
	values[valuesById[17404] = "S2CGetStateDemons_ID"] = 17404;
	values[valuesById[17405] = "C2SStateBreach_ID"] = 17405;
	values[valuesById[17406] = "S2CStateBreach_ID"] = 17406;
	values[valuesById[17407] = "C2SStateUsePill_ID"] = 17407;
	values[valuesById[17408] = "S2CStateUsePill_ID"] = 17408;
	values[valuesById[17409] = "C2SBuyStateGift_ID"] = 17409;
	values[valuesById[17410] = "S2CBuyStateGift_ID"] = 17410;
	var msg = {
		S2SServerInfo: {
			ServerId:0,
			ServerType:0,
		}
		,
		S2SRequestMsg: {
			RequestID:0,
			CallType:0,
			ServerId:0,
			UserId:0,
			Pck:0,
		}
		,
		S2SResponseMsg: {
			RequestID:0,
			Tag:0,
			Pck:0,
		}
		,
		S2SServerPlayerPck: {
			UserId:0,
			Pck:0,
		}
		,
		S2SServerPlayerHandle: {
			UserId:0,
			Pck:0,
		}
		,
		S2KPlayerData: {
			UserId:0,
			Player:"RoleDbInfo",
		}
		,
		Ping: {
			T:0,
		}
		,
		Pong: {
			T:0,
		}
		,
		S2KCrossPing: {
			UserId:0,
		}
		,
		CommonType: {
			A:0,
			B:0,
		}
		,
		S2CServerTime: {
			T:0,
		}
		,
		S2CServerAge: {
			T:0,
		}
		,
		IntAttr: {
			k:0,
			v:0,
		}
		,
		StrAttr: {
			k:0,
			v:'',
		}
		,
		ItemInfo: {
			ItemId:0,
			ItemNum:0,
			Bind:0,
			ItemValid:0,
			DSL:0,
			DSS:0,
		}
		,
		ItemData: {
			Id:'',
			Pos:0,
			IId:0,
			N:0,
			B:0,
			A:0,
			V:0,
			DSL:0,
			DSS:0,
			DSPos:0,
			Attr:["IntAttr"],
			Star:0,
		}
		,
		ItemNum: {
			IId:0,
			N:0,
		}
		,
		Pos: {
			X:0,
			Y:0,
		}
		,
		LimitTimesData: {
			Id:0,
			P:0,
			Use:0,
		}
		,
		RoleLimitTimes: {
			Items:["LimitTimesData"],
		}
		,
		RoleDbInfo: {
			UserId:0,
			Nick:'',
			RoleId:0,
			Sex:0,
			intAttr:["IntAttr"],
			strAttr:["StrAttr"],
			ActiveSkills:["Skill"],
			Skins:["Skin"],
			Bag:"RoleBag",
			Grade:"RoleGrade",
			LimitTimes:"RoleLimitTimes",
			Counters:["Counter"],
			Tasks:["Task"],
			Mails:"RoleMail",
			Pets:["Pet"],
			PetAs:["PetA"],
			Kids:["Kid"],
			SkyGods:["SkyGod"],
			Beasts:["Beast"],
			PartnerSuit:["int32"],
			VIPPrize:["int32"],
			MarryInfo:"DbMarryInfo",
			Precious:["Precious"],
			PlayerWestExp:"DbPlayerWestExp",
			GangExc:"GangExchange",
			SendShopItems:["int32"],
			DragonLogs:["DragonLogItem"],
			StagePrize:["StagePrizeInfo"],
			Rprs:"RedPkgRecords",
			SMMonster:["SMMonsterInfo"],
			CreateCpsId:0,
			NewPrize:["int32"],
			AchieveShow:["int32"],
			FriendInfo:"RoleFriend",
			MasterInfo:"DbMasterData",
			DailyTask:["DBDailyTask"],
			JJCChange:["JJCChange"],
			Devils:["Devil"],
			Beauty:"Beauty",
			BossHill:"BossHillData",
			Player81:["Player81Data"],
			HTI:"S2CHistoryTaskInfo",
			TreasureData:"TreasureData",
			PTasks:["FinishTask"],
			FTasks:["FinishTask"],
			GodItem:["GodItem"],
			RedPckLog:["RedPckLog"],
			HolyBeasts:["HolyBeast"],
			HolyBeastSuitLevel:0,
			ExpeditionData:"ExpeditionData",
			Toys:["Toy"],
			RobberyItem:["RobberyItem"],
			FairySheets:["FairySheet"],
			FairyAchievements:["FairyAchievement"],
			RobberData:["RobberData"],
			ActPlayerData:["ActPlayerData"],
			ActExit:["int32"],
			RobberyLogs:["RobberyLogItem"],
			JJCPrizeTime:0,
			VIPGift:["int32"],
			DevilPos:["DevilPos"],
			PlayerDragon:"PlayerDragon",
			ClientDb:"S2CClientDb",
			FairyDareData:["S2CPlayerDareBossInfo"],
			RoleGodPrize:["int32"],
			CreateServerId:0,
			PassCheckData:["PassCheckItem"],
			ZF:["ZF"],
			PreciousPos:["PreciousPos"],
			PhotoBooks:["PhotoBook"],
			ItemAttr:["IntAttr"],
			ChargeGift:["int32"],
			YesterdayTasks:["Task"],
			StarLuck:["IntAttr"],
			StageGift:["int32"],
		}
		,
		RobberData: {
			Id:0,
			Times:0,
		}
		,
		TreasureData: {
			Max3Star:0,
			Stage:["int32"],
			MaxPrize:0,
			GetPrize:["int32"],
			MaxBoxId:0,
			BoxIds:["int32"],
		}
		,
		JJCChange: {
			T:0,
			R:0,
		}
		,
		BaseDbInfo: {
			intAttr:["IntAttr"],
			strAttr:["StrAttr"],
		}
		,
		Skin: {
			Id:0,
			W:0,
			V:0,
			Old:0,
			L:0,
		}
		,
		RoleBaseInfo: {
			UserId:0,
			Nick:'',
			LogoutTime:0,
			FightValue:0,
			VipLevel:0,
			RoleId:0,
			OnLine:0,
		}
		,
		C2SChangeNick: {
			Nick:'',
		}
		,
		S2CChangeNick: {
			Tag:0,
		}
		,
		PlayerChargeData: {
			UserId:0,
			GoodsId:0,
			ChannelOrderId:'',
			PfOrderId:'',
			ChannelId:0,
			RealAmount:0,
			ExtraInfo:'',
		}
		,
		KingData: {
			State:0,
			StartTime:0,
			PlayerData:["KingPlayerData"],
			LastWeekPlayerData:["KingPlayerData"],
		}
		,
		KingPlayerData: {
			Id:0,
			KingRank:0,
			Win:0,
			ContinueWin:0,
			Times:0,
			NextTimes:0,
			IsRobot:0,
		}
		,
		JJCData: {
			Id:0,
			JJC:0,
		}
		,
		ServerData: {
			DragonLogs:["DragonLogItem"],
			ActGangWar:"ActGangWarPreInfo",
			FirstNick:'',
			PlayerCharge:["PlayerChargeData"],
			Instance81:["Instance81Data"],
			WorldBossKillData:"WorldBossKillData",
			KingData:"KingData",
			RobberyLogs:["RobberyLogItem"],
			JJCData:["JJCData"],
			ActGroupData:["ActGroupData"],
			NowDay:0,
			GangCreateTime:0,
			DrawLog:["DrawDbLog"],
			ChargeReturnLog:["ChargeReturnDbLog"],
			ResetChargeTime:0,
		}
		,
		ActGroupData: {
			GroupId:0,
			RoundNum:0,
			OrderIdx:0,
		}
		,
		CrossServerData: {
			NineLastTimeDay:0,
		}
		,
		S2CServerInfo: {
			IsOpenRealName:0,
			IsOpenFcm:0,
			SendShopMaxNum:0,
			OpenDragon:0,
			IsOpenSend:0,
		}
		,
		Default: {
		}
		,
		MAX: {
		}
		,
		C2SLogin: {
			AccountId:0,
			Token:'',
			UserId:0,
			Fcm:0,
			LoginPf:'',
		}
		,
		S2CLogin: {
			Tag:0,
			UserId:0,
		}
		,
		C2SReLogin: {
			AccountId:0,
			Token:'',
			UserId:0,
		}
		,
		S2CReLogin: {
			Tag:0,
		}
		,
		C2SLoginEnd: {
		}
		,
		S2CAllData: {
		}
		,
		C2SUserInfo: {
			AccountId:0,
			Token:'',
		}
		,
		S2CUserInfo: {
			Tag:0,
			MapId:0,
			MapX:0,
			MapY:0,
			A:["IntAttr"],
		}
		,
		S2COfflinePrize: {
			Gold:0,
			Exp:0,
			Equip:0,
			LogoutTime:0,
		}
		,
		C2SGetOfflinePrize: {
			Multi:0,
		}
		,
		S2CGetOfflinePrize: {
			Tag:0,
		}
		,
		LoginFinish: {
		}
		,
		C2SGetGift1: {
		}
		,
		S2CGetGift1: {
			Tag:0,
		}
		,
		C2SGetGift2: {
		}
		,
		S2CGetGift2: {
			Tag:0,
		}
		,
		C2SGetMonthGift: {
		}
		,
		S2CGetMonthGift: {
			Tag:0,
		}
		,
		C2SFocusPrice: {
			Type:0,
		}
		,
		S2CFocusPrice: {
			Tag:0,
		}
		,
		C2SFirstInvite: {
		}
		,
		S2CFirstInvite: {
			Tag:0,
		}
		,
		C2SNewStory: {
		}
		,
		S2CNewStory: {
			Tag:0,
		}
		,
		S2CRoleInfo: {
			UserId:0,
			A:["IntAttr"],
			B:["StrAttr"],
		}
		,
		C2SOtherInfo: {
			UserId:0,
		}
		,
		S2COtherInfo: {
			UserId:0,
			A:["IntAttr"],
			B:["StrAttr"],
			MateNick:'',
			Tag:0,
		}
		,
		S2CRoleData: {
			ActiveSkills:["Skill"],
			Skins:["Skin"],
			VIPPrize:["int32"],
			Task:["S2CTask"],
			Counters:["S2CCounter"],
			WorldLevel:"WorldLevel",
			Precious:["Precious"],
			NewPrize:["int32"],
			VIPGift:["int32"],
			ChargeGift:["int32"],
			StateGift:["int32"],
		}
		,
		C2SAchieveShow: {
			AchieveShow:0,
		}
		,
		S2CAchieveShow: {
			AchieveShow:["int32"],
		}
		,
		S2CRoleBaseInfo: {
			UserId:0,
			Nick:'',
			LogoutTime:0,
			FightValue:0,
			VipLevel:0,
			Level:0,
			RoleId:0,
			OnLine:0,
		}
		,
		C2SRoleBaseInfo: {
			UserId:0,
		}
		,
		S2CKill: {
			Tag:0,
		}
		,
		FightUnitData: {
			I:0,
			T:0,
			H:0,
			A:["IntAttr"],
			B:["StrAttr"],
			Skill:["Skill"],
		}
		,
		PlayerFightUnit: {
			UserId:0,
			Nick:'',
			FightValue:0,
			RoleId:0,
			Units:["FightUnitData"],
		}
		,
		FightUnit: {
			P:0,
			I:0,
			T:0,
			A:["IntAttr"],
			B:["StrAttr"],
			F:0,
			H:0,
			MH:0,
			H1:0,
			MH1:0,
			IsMH1:0,
		}
		,
		Effect: {
			K:0,
			V:0,
			Protect:0,
		}
		,
		AtkUnit: {
			P:0,
			E:["Effect"],
		}
		,
		FightStep: {
			P:0,
			S:0,
			U:["AtkUnit"],
			R:0,
			E:["Effect"],
		}
		,
		S2CBattlefieldReport: {
			U:["FightUnit"],
			S:["FightStep"],
			I:["ItemInfo"],
			T:0,
			P:0,
			Win:0,
			Idx:0,
			M:0,
			PVP:0,
			Num:0,
			Video:0,
		}
		,
		DbBattlefieldReport: {
			Data:["int32"],
		}
		,
		S2KLoginSuccess: {
			UserId:0,
		}
		,
		K2SBattlefieldReport: {
			UserId:0,
			IsLeader:0,
			Data:0,
		}
		,
		K2SEnterMap: {
			UserId:0,
			MapId:0,
			InstanceId:'',
			X:0,
			Y:0,
		}
		,
		S2KEnterMap: {
			UserId:0,
			MapId:0,
			InstanceId:'',
			X:0,
			Y:0,
		}
		,
		S2KLeaveMap: {
			MapId:0,
			InstanceId:'',
		}
		,
		S2KLeaveInstanceTeam: {
			TeamId:0,
		}
		,
		S2KLogout: {
		}
		,
		S2KReviveLife: {
		}
		,
		S2KWorldLevel: {
			Level:0,
		}
		,
		S2KCreateServerIds: {
			Ids:["int32"],
		}
		,
		K2SGetPlayerByLevel: {
			MinLevel:0,
			MaxLevel:0,
		}
		,
		S2KGetPlayerOtherInfo: {
			UserId:0,
			OtherId:0,
		}
		,
		K2SAddItem: {
			Items:["ItemData"],
			Wait:0,
			ChangType:0,
			IntParam:["int32"],
			StrParam:[],
			UId:0,
		}
		,
		K2SAddRedPkg: {
			Type:0,
			Money:0,
		}
		,
		K2SChangeAttr: {
			UserId:0,
			A:["IntAttr"],
			B:["StrAttr"],
		}
		,
		K2SUnlockSkin: {
			UserId:0,
			SkinId:0,
		}
		,
		K2SDeleteSkin: {
			UserId:0,
			SkinId:0,
		}
		,
		K2SMail: {
			UserId:0,
			TmpId:0,
			Itemdata:["ItemData"],
			ChangeType:0,
			Param:'',
		}
		,
		K2SActState: {
			ActId:0,
			State:0,
			OverTime:0,
		}
		,
		K2SBroadcastGang: {
			GangId:'',
			Pck:0,
		}
		,
		C2SEndFight: {
			Idx:0,
		}
		,
		S2CPrizeReport: {
			Items:["ItemData"],
			Idx:0,
			Type:0,
			Star:0,
			JJC:0,
			FBType:0,
			FightNick:'',
		}
		,
		C2SStageFight: {
		}
		,
		S2CStageFight: {
			Tag:0,
		}
		,
		C2SAutoStage: {
			State:0,
		}
		,
		C2SStageSeek: {
		}
		,
		S2CStageSeek: {
			Tag:0,
			UserId:0,
			StageId:0,
		}
		,
		C2SStageHelp: {
			UserId:0,
			StageId:0,
		}
		,
		S2CStageHelp: {
			Tag:0,
		}
		,
		C2SChangeMap: {
			MapId:0,
		}
		,
		S2CChangeMap: {
			Tag:0,
			MapId:0,
			X:0,
			Y:0,
		}
		,
		C2SAutoJump: {
			V:0,
		}
		,
		StagePrizeInfo: {
			Id:0,
			Num:0,
		}
		,
		C2SStagePrize: {
		}
		,
		S2CStagePrize: {
			Items:["StagePrizeInfo"],
		}
		,
		C2SGetStagePrize: {
			Id:0,
		}
		,
		S2CGetStagePrize: {
			Tag:0,
			Id:0,
		}
		,
		C2SGetAllStagePrize: {
		}
		,
		S2CGetAllStagePrize: {
			Tag:0,
		}
		,
		C2SCatchPet: {
		}
		,
		S2CCatchPet: {
			Tag:0,
			Win:0,
			ItemId:0,
			MonsterId:0,
		}
		,
		C2SStartMove: {
			P:["int32"],
		}
		,
		S2CStartMove: {
			Tag:0,
		}
		,
		S2CPlayerMove: {
			UserId:0,
			P:["int32"],
		}
		,
		C2SStopMove: {
			X:0,
			Y:0,
		}
		,
		S2CStopMove: {
		}
		,
		S2CPlayerStopMove: {
			UserId:0,
			X:0,
			Y:0,
		}
		,
		S2CTransfer: {
			UserId:0,
			X:0,
			Y:0,
		}
		,
		C2SCheckFight: {
		}
		,
		S2CCheckFight: {
			Tag:0,
			NextTime:0,
		}
		,
		S2CPlayerEnterMap: {
			UserId:0,
			A:["IntAttr"],
			B:["StrAttr"],
			X:0,
			Y:0,
			P:["int32"],
			MT:0,
		}
		,
		S2CPlayerLeaveMap: {
			UserId:0,
		}
		,
		S2CMonsterEnterMap: {
			Id:0,
			IID:0,
			X:0,
			Y:0,
			MHp:0,
			Hp:0,
			MHp1:0,
			Hp1:0,
			Fx:0,
		}
		,
		S2CUpMonsterInfo: {
			MHp:0,
			Hp:0,
			MHp1:0,
			Hp1:0,
			Id:0,
			LifeState:0,
			IsCollect:0,
		}
		,
		S2CMonsterLeaveMap: {
			Id:0,
		}
		,
		C2SStartFight: {
			Id:0,
			Type:0,
		}
		,
		S2CStartFight: {
			Tag:0,
		}
		,
		C2SStartCollect: {
			Id:0,
		}
		,
		S2CStartCollect: {
			Tag:0,
		}
		,
		C2SEndCollect: {
			Id:0,
			Ret:0,
		}
		,
		S2CEndCollect: {
			Tag:0,
			Ret:0,
		}
		,
		S2CMonsterTalk: {
			Id:0,
			TalkId:0,
		}
		,
		Skill: {
			I:0,
			L:0,
		}
		,
		C2SRoleSkillUp: {
			Id:0,
		}
		,
		C2SRoleSkillUpAuto: {
		}
		,
		S2CRoleSkillUp: {
			Tag:0,
			Skill:["Skill"],
		}
		,
		C2SRoleSkillOrder: {
			Skill:["Skill"],
		}
		,
		S2CRoleSkillOrder: {
			Tag:0,
		}
		,
		C2SUnlockSkin: {
			SkinId:0,
		}
		,
		S2CUnlockSkin: {
			Tag:0,
			Skins:["Skin"],
		}
		,
		C2SWearSkin: {
			SkinId:0,
		}
		,
		S2CWearSkin: {
			Tag:0,
			SkinId:0,
		}
		,
		C2SSkinUp: {
			SkinId:0,
		}
		,
		S2CSkinUp: {
			Tag:0,
			SkinId:0,
			SkinLevel:0,
		}
		,
		C2SGetVipPrize: {
			VIPLevel:0,
		}
		,
		S2CGetVipPrize: {
			Tag:0,
			VIPLevel:0,
		}
		,
		C2SBuyVipPrize: {
			VIPLevel:0,
		}
		,
		S2CBuyVipPrize: {
			Tag:0,
			VIPLevel:0,
		}
		,
		C2SGetNewPrize: {
			Id:0,
		}
		,
		S2CGetNewPrize: {
			Tag:0,
			Id:0,
		}
		,
		C2SClientSwitch: {
			Value:0,
		}
		,
		S2CClientSwitch: {
			Tag:0,
		}
		,
		C2SShareSuccess: {
		}
		,
		S2CUserGrade: {
			Grade:["Grade"],
		}
		,
		C2SRoleLevelUp: {
		}
		,
		S2CRoleLevelUp: {
			Tag:0,
		}
		,
		RoleGrade: {
			Grade:["Grade"],
		}
		,
		Grade: {
			DT:0,
			PT:0,
			L:0,
			P:0,
		}
		,
		C2SGradeUp: {
			GradeType:0,
			PartType:0,
			Times:0,
			AutoBuy:0,
		}
		,
		S2CGradeUp: {
			Tag:0,
			Grade:"Grade",
			Multi:0,
			New:0,
		}
		,
		C2SGrade2: {
			T:0,
		}
		,
		S2CGrade2: {
			Tag:0,
			T:0,
		}
		,
		C2SEquipGradeUp: {
			GradeType:0,
		}
		,
		S2CEquipGradeUp: {
			Tag:0,
			Grade:["Grade"],
		}
		,
		C2SRoleGodGradeUp: {
		}
		,
		S2CRoleGodGradeUp: {
			Tag:0,
			Level:0,
		}
		,
		C2SGetRoleGodPrize: {
			Level:0,
		}
		,
		S2CGetRoleGodPrize: {
			Tag:0,
			Level:0,
		}
		,
		S2CRoleGodPrize: {
			Prize:["int32"],
		}
		,
		RoleFriend: {
			FriendList:["DBFriend"],
		}
		,
		DBFriend: {
			FId:0,
			T:0,
			Cs:0,
			E:0,
		}
		,
		Friend: {
			F:"DBFriend",
			A:0,
			N:'',
			L:0,
			S:0,
			V:0,
			O:0,
			Gl:0,
			Gn:'',
		}
		,
		S2CGetFriends: {
			F:0,
			Tf:0,
			A:0,
			Ta:0,
			B:0,
			Tb:0,
			S:0,
			Ts:0,
			R:0,
			Tr:0,
			List:["Friend"],
		}
		,
		FriendSendMessage: {
			List:["Friend"],
			FriendId:0,
		}
		,
		C2SFocus: {
			FriendId:0,
		}
		,
		S2CFocus: {
			Tag:0,
			FriendId:0,
		}
		,
		C2SCancelFocus: {
			FriendId:0,
		}
		,
		S2CCancelFocus: {
			Tag:0,
			FriendId:0,
		}
		,
		C2SHate: {
			FriendId:0,
		}
		,
		S2CHate: {
			Tag:0,
			FriendId:0,
		}
		,
		C2SCancelHate: {
			FriendId:0,
		}
		,
		S2CCancelHate: {
			Tag:0,
			FriendId:0,
		}
		,
		C2SGiveCoin: {
			FriendId:0,
		}
		,
		S2CGiveCoin: {
			Tag:0,
			FriendId:0,
		}
		,
		C2SGetCoin: {
			FriendId:0,
		}
		,
		S2CGetCoin: {
			Tag:0,
			FriendId:0,
		}
		,
		C2SGetSuggest: {
		}
		,
		S2CGetSuggest: {
			List:["Friend"],
		}
		,
		C2SOneKeyGiveCoin: {
		}
		,
		S2COneKeyGiveCoin: {
			Tag:0,
			Num:0,
		}
		,
		C2SOneKeyGetCoin: {
		}
		,
		S2COneKeyGetCoin: {
			Tag:0,
			Num:0,
		}
		,
		C2SOneKeyFocus: {
		}
		,
		S2COneKeyFocus: {
			Tags:["int32"],
			Num:0,
		}
		,
		ChatMsg: {
			ID:0,
			ChannelId:0,
			Type:0,
			Content:'',
			SenderId:0,
			SenderNick:'',
			SenderRoleId:0,
			SenderGodLevel:0,
			AreaId:0,
		}
		,
		C2SSendChatMsg: {
			ChannelId:0,
			Content:'',
		}
		,
		S2CSendChatMsg: {
			Tag:0,
		}
		,
		C2SGetHistoryChat: {
		}
		,
		S2CGetHistoryChat: {
			Chatmessage:["ChatMsg"],
		}
		,
		S2CNewChatMsg: {
			Chatmessage:"ChatMsg",
		}
		,
		WhisperMsg: {
			SenderId:0,
			SenderNick:'',
			SenderRoleId:0,
			SenderGodLevel:0,
			ReceiverId:0,
			ReceiverNick:'',
			ReceiverRoleId:0,
			ReceiverGodLevel:0,
			Content:'',
			Time:0,
		}
		,
		C2SWhisper: {
			ReceiverId:0,
			Content:'',
		}
		,
		S2CWhisper: {
			Tag:0,
			M:"WhisperMsg",
		}
		,
		C2SGetWhisper: {
			PlayerId:0,
		}
		,
		S2CGetWhisper: {
			m:["WhisperMsg"],
			TargetId:0,
		}
		,
		S2CAllUnreadWhisper: {
			m:["WhisperMsg"],
		}
		,
		C2SGetOnlineStatus: {
			UidList:["int32"],
		}
		,
		PlayerOnlineStatus: {
			Uid:0,
			Status:0,
		}
		,
		S2CGetOnlineStatus: {
			List:["PlayerOnlineStatus"],
		}
		,
		C2SRemoveWhisper: {
			PlayerId:0,
		}
		,
		S2CRemoveWhisper: {
			Tag:0,
		}
		,
		RoleBag: {
			Items:["ItemData"],
			Size:0,
			Auto:0,
			ExtendBagSize:0,
		}
		,
		BagChange: {
			T:0,
			Item:"ItemData",
		}
		,
		S2CBagChange: {
			Change:["BagChange"],
		}
		,
		S2CNewItem: {
			Change:["BagChange"],
		}
		,
		C2SUserBag: {
		}
		,
		S2CUserBag: {
			Bag:"RoleBag",
		}
		,
		C2SExtendBag: {
			Count:0,
		}
		,
		S2CExtendBag: {
			Tag:0,
			Size:0,
		}
		,
		C2SExchange: {
			ItemID:'',
			Count:0,
			Param1:0,
		}
		,
		S2CExchange: {
			Tag:0,
		}
		,
		C2SGoldBag: {
			Buy:0,
		}
		,
		S2CGoldBag: {
			Tag:0,
		}
		,
		C2SGetGoldBagInfo: {
		}
		,
		S2CGetGoldBagInfo: {
			TodayRamin:0,
			NextVipTimes:0,
		}
		,
		C2SWearEquip: {
			Type:0,
		}
		,
		S2CWearEquip: {
			Tag:0,
		}
		,
		C2SWearOneEquip: {
			ItemId:'',
		}
		,
		S2CWearOneEquip: {
			Tag:0,
		}
		,
		C2SEquipStar: {
			ItemId:'',
			Auto:0,
		}
		,
		S2CEquipStar: {
			Tag:0,
			Ret:0,
		}
		,
		C2SMeltEquip: {
			Items:[],
		}
		,
		S2CMeltEquip: {
			Tag:0,
		}
		,
		C2SOpenTreasure: {
			ItemID:'',
			Count:0,
		}
		,
		S2COpenTreasure: {
			Tag:0,
		}
		,
		C2SAutoMelt: {
		}
		,
		S2CAutoMelt: {
			Tag:0,
			Auto:0,
			Melt:0,
		}
		,
		C2SMeltGoldByItem: {
			PartId:0,
		}
		,
		S2CMeltGoldByItem: {
			Tag:0,
		}
		,
		C2SGetBattlePrize: {
		}
		,
		ItemFly: {
			Item:["ItemNum"],
		}
		,
		C2SGetShopMallList: {
			MallType:0,
		}
		,
		ShopGoodsInfo: {
			GoodsId:0,
			LimitBuy:0,
			LimitUseTimes:0,
			LimitTotalTimes:0,
			MallType:0,
			Sort:0,
			AddFight:0,
		}
		,
		S2CGetShopMallList: {
			GoodsData:["ShopGoodsInfo"],
			LimitValue:0,
			MallType:0,
		}
		,
		C2SShopBuy: {
			GoodsId:0,
			Num:0,
		}
		,
		S2CShopBuy: {
			Tag:0,
			GoodsId:0,
			LimitBuy:0,
			LimitUseTimes:0,
			LimitTotalTimes:0,
			MallType:0,
		}
		,
		C2SOrgMallList: {
			MallType:0,
		}
		,
		S2COrgMallList: {
			GoodsData:["ShopGoodsInfo"],
		}
		,
		C2SOrgBuy: {
			GoodsId:0,
			Num:0,
		}
		,
		S2COrgBuy: {
			Tag:0,
			MallType:0,
		}
		,
		C2SGetGoodsLimit: {
			GoodsId:0,
		}
		,
		S2CGetGoodsLimit: {
			Tag:0,
			LimitUseTimes:0,
			LimitTotalTimes:0,
			GoodsId:0,
		}
		,
		DbSendShop: {
			DbId:'',
			ItemData:"ItemData",
			TotalNum:0,
			LeftNum:0,
			EndTime:0,
			Uid:0,
			Sid:0,
			Price:0,
			status:0,
			LeftCoin3:0,
			LockTime:0,
		}
		,
		DbSaleLog: {
			BuyerId:0,
			ItemId:0,
			Num:0,
			BuyTime:0,
			Price:0,
		}
		,
		C2SGetSendShopList: {
			Td:0,
			Page:0,
			Size:0,
		}
		,
		S2KGetSendShopList: {
			Items:["int32"],
			Td:0,
			Page:0,
			Size:0,
		}
		,
		SendShopGroup: {
			Iid:0,
			Sn:0,
			M:0,
		}
		,
		S2CGetSendShopList: {
			List:["SendShopGroup"],
			Td:0,
			Tag:0,
			Page:0,
			Total:0,
		}
		,
		C2SGetSendShopById: {
			Iid:0,
			Page:0,
			Size:0,
		}
		,
		SendShopSingle: {
			ItemData:"ItemData",
			P:0,
			Ln:0,
			N:0,
			Et:0,
			Lid:'',
		}
		,
		S2CGetSendShopById: {
			Iid:0,
			List:["SendShopSingle"],
			Page:0,
			Total:0,
		}
		,
		SellItem: {
			ItemData:"ItemData",
			L:0,
			U:0,
			P:0,
			OtherList:["SendShopSingle"],
		}
		,
		C2SGetMySendShop: {
		}
		,
		S2KGetMySendShopList: {
			PlayerId:0,
			Items:["ItemData"],
			Lt:0,
			Page:0,
			Total:0,
		}
		,
		S2CGetMySendShop: {
			List:["SendShopSingle"],
			ItemList:["SellItem"],
			Lt:0,
		}
		,
		C2SSendShopOnSale: {
			ItemData:"ItemData",
			P:0,
		}
		,
		S2KSendShopOnSale: {
			PlayerId:0,
			ServerId:0,
			ItemData:"ItemData",
			Price:0,
			OrderId:'',
		}
		,
		S2CSendShopOnSale: {
			Tag:0,
			SaleCount:0,
		}
		,
		C2SSendShopBuy: {
			Lid:'',
			N:0,
		}
		,
		S2KSendShopPreBuy: {
			Lid:'',
			Num:0,
		}
		,
		K2SSendShopPreBuy: {
			Tag:0,
			Price:0,
			ItemId:0,
			Num:0,
			Lid:'',
		}
		,
		S2KSendShopBuy: {
			Lid:'',
			Num:0,
			PlayerId:0,
			OrderId:'',
		}
		,
		K2SSendShopBuy: {
			Lid:'',
			ItemData:"ItemData",
			Coin3:0,
			PlayerId:0,
			Tag:0,
			OrderId:'',
		}
		,
		S2CSendShopBuy: {
			Tag:0,
		}
		,
		C2SSendShopClear: {
			Lid:'',
			IsLower:0,
		}
		,
		S2KSendShopClear: {
			PlayerId:0,
			Lid:'',
			IsLower:0,
		}
		,
		ClearInfo: {
			ItemData:"ItemData",
			Status:0,
			Money:0,
			LeftNum:0,
			EndTime:0,
			TotalNum:0,
			Lid:'',
		}
		,
		K2SSendShopClear: {
			Items:["ClearInfo"],
			SaleCount:0,
			OrderId:'',
			IsLower:0,
		}
		,
		S2CSendShopClear: {
			Tag:0,
			IsLower:0,
		}
		,
		SaleItemLog: {
			Iid:0,
			N:0,
			Gn:0,
			St:0,
		}
		,
		C2SSaleLog: {
		}
		,
		S2KGetSaleLog: {
			PlayerId:0,
		}
		,
		S2CSaleLog: {
			List:["SaleItemLog"],
		}
		,
		C2SMarkSendShop: {
			Iid:0,
			A:0,
		}
		,
		S2CMarkSendShop: {
			Tag:0,
			Iid:0,
			A:0,
		}
		,
		C2SGetSendCfg: {
			ItemId:0,
		}
		,
		S2CGetSendCfg: {
			T:0,
		}
		,
		SendUserInfo: {
			UserId:0,
			Nick:'',
			FightValue:0,
			Level:0,
			VipLevel:0,
			RoleId:0,
			GodLevel:0,
			AreaId:0,
			ServerId:0,
		}
		,
		C2SGetSendUserInfo: {
			UseId:0,
		}
		,
		S2CGetSendUserInfo: {
			Tag:0,
			Info:"SendUserInfo",
		}
		,
		C2SSendItem: {
			Uid:0,
			Iid:0,
			In:0,
		}
		,
		S2CSendItem: {
			Tag:0,
		}
		,
		Counter: {
			T:0,
			C:0,
			UT:'',
			P:0,
		}
		,
		S2CCounter: {
			T:0,
			C:0,
			P:0,
		}
		,
		Task: {
			Id:0,
			T:0,
			IC:0,
			C:0,
		}
		,
		FinishTask: {
			Id:0,
			T:0,
			IC:0,
			C:0,
		}
		,
		S2CTask: {
			Id:0,
			T:0,
			S:0,
			IC:0,
		}
		,
		DBDailyTask: {
			T:["DailyTask"],
			Key:'',
		}
		,
		DailyTask: {
			Uid:0,
			ID:0,
			T:0,
			S:0,
			I:0,
			C:0,
		}
		,
		C2SGetTaskPrize: {
			TaskId:0,
			TaskType:0,
			Multi:0,
		}
		,
		S2CGetTaskPrize: {
			Tag:0,
			Prize:["ItemInfo"],
		}
		,
		S2CHistoryTaskInfo: {
			MainTaskId:0,
			NowStage:0,
			NewMapId:0,
			StateLevel:0,
		}
		,
		C2SGetHistoryTaskPrize: {
			TaskId:0,
		}
		,
		S2CGetHistoryTaskPrize: {
			Tag:0,
			Prize:["ItemInfo"],
			NextTask:"S2CTask",
		}
		,
		LifeRemain: {
			TaskId:0,
			Type:0,
			Times:0,
		}
		,
		S2CLastDayRemain: {
			List:["LifeRemain"],
		}
		,
		C2SLifeFind: {
			TaskId:0,
			Type:0,
			Count:0,
		}
		,
		S2CLifeFind: {
			Tag:0,
			Remain:"LifeRemain",
		}
		,
		C2SLifeFastFind: {
		}
		,
		S2CLifeFastFind: {
			Tag:0,
		}
		,
		C2SWorldLevel: {
		}
		,
		WorldLevel: {
			Level:0,
			Players:["Rank"],
		}
		,
		C2SPeaceFinish: {
		}
		,
		S2CPeaceFinish: {
			Tag:0,
		}
		,
		C2SSignPrize: {
		}
		,
		S2CSignPrize: {
			Tag:0,
		}
		,
		SMMonsterInfo: {
			RefreshId:0,
			Star:0,
		}
		,
		SMMonster: {
			Id:0,
			Star:0,
		}
		,
		S2CSMMonster: {
			Monster:["SMMonster"],
		}
		,
		C2SSMFight: {
			Monster:"SMMonster",
		}
		,
		S2CSMFight: {
			Tag:0,
			Prize:["ItemInfo"],
		}
		,
		C2SSMRefreshStar: {
			Monster:"SMMonster",
		}
		,
		S2CSMRefreshStar: {
			Tag:0,
			OldMonster:"SMMonster",
			NewMonster:"SMMonster",
		}
		,
		C2SSMFastFinish: {
		}
		,
		S2CSMFastFinish: {
			Tag:0,
			Prize:["ItemInfo"],
		}
		,
		MailInfo: {
			MailId:0,
			MailTplId:0,
			MailTplParam:'',
			Title:'',
			MailType:0,
			IsReceive:0,
			ReceiveTime:'',
			Content:'',
			AttachInfo:["ItemInfo"],
			AttachData:["ItemData"],
		}
		,
		DbMailInfo: {
			MailId:0,
			MailTplId:0,
			MailTplParam:'',
			MailType:0,
			IsReceive:0,
			Content:'',
			ReceiveTime:0,
			ChangeType:0,
			SendUserId:0,
			SendUserNick:'',
			Title:'',
			AttachInfo:["ItemInfo"],
			AttachData:["ItemData"],
		}
		,
		RoleMail: {
			SystemMailId:0,
			UserMailId:0,
			DbMailId:0,
			DbMailInfo:["DbMailInfo"],
		}
		,
		C2SMailList: {
		}
		,
		S2CMailList: {
			MailList:["MailInfo"],
		}
		,
		C2SGetMailAttach: {
			MailId:0,
		}
		,
		S2CGetMailAttach: {
			Tag:0,
		}
		,
		C2SDelMail: {
			MailId:0,
		}
		,
		S2CDelMail: {
			Tag:0,
		}
		,
		C2SGM: {
			Data:'',
		}
		,
		S2CNewMail: {
		}
		,
		BossPersonalInfo: {
			Id:0,
			K:0,
		}
		,
		S2CBossPersonalInfo: {
			Items:["BossPersonalInfo"],
		}
		,
		C2SBossPersonalFight: {
			Id:0,
		}
		,
		S2CBossPersonalFight: {
			Tag:0,
			Id:0,
			Win:0,
		}
		,
		C2SBossPersonalSweep: {
		}
		,
		S2CBossPersonalSweep: {
			Tag:0,
			Ids:["int32"],
		}
		,
		InstanceMaterialInfo: {
			Id:0,
			K:0,
			L:0,
			V:0,
			C:0,
		}
		,
		S2CInstanceMaterialInfo: {
			Items:["InstanceMaterialInfo"],
		}
		,
		C2SInstanceMaterialFight: {
			Id:0,
		}
		,
		S2CInstanceMaterialFight: {
			Tag:0,
			info:"InstanceMaterialInfo",
		}
		,
		C2SInstanceMaterialSweep: {
			Id:0,
		}
		,
		S2CInstanceMaterialSweep: {
			Tag:0,
			info:["InstanceMaterialInfo"],
		}
		,
		InstanceTreasureInfo: {
			Id:0,
			S:0,
			L:0,
		}
		,
		S2CInstanceTreasureInfo: {
			Items:["InstanceTreasureInfo"],
			B:["int32"],
		}
		,
		C2SInstanceTreasureFight: {
			Id:0,
		}
		,
		S2CInstanceTreasureFight: {
			Tag:0,
			Star:0,
			Id:0,
		}
		,
		C2SInstanceTreasureSweep: {
		}
		,
		S2CInstanceTreasureSweep: {
			Tag:0,
			Ids:["int32"],
		}
		,
		C2SGetInstanceTreasureBox: {
			Id:0,
		}
		,
		S2CGetInstanceTreasureBox: {
			Tag:0,
			Ids:["int32"],
		}
		,
		S2CInstanceHeavenlyInfo: {
			M:0,
			D:0,
			B:["int32"],
		}
		,
		C2SInstanceHeavenlyFight: {
			Id:0,
		}
		,
		S2CInstanceHeavenlyFight: {
			Tag:0,
			Id:0,
			Win:0,
		}
		,
		C2SInstanceHeavenlySweep: {
		}
		,
		S2CInstanceHeavenlySweep: {
			Tag:0,
			D:0,
		}
		,
		C2SGetInstanceHeavenlyBox: {
			Id:0,
		}
		,
		S2CGetInstanceHeavenlyBox: {
			Tag:0,
			Id:0,
		}
		,
		S2CInstanceDemonInfo: {
			M:0,
			D:0,
			B:["int32"],
		}
		,
		C2SInstanceDemonFight: {
			Id:0,
		}
		,
		S2CInstanceDemonFight: {
			Tag:0,
			Id:0,
			Win:0,
		}
		,
		C2SInstanceDemonSweep: {
		}
		,
		S2CInstanceDemonSweep: {
			Tag:0,
			D:0,
		}
		,
		C2SGetInstanceDemonBox: {
			Id:0,
		}
		,
		S2CGetInstanceDemonBox: {
			Tag:0,
			Id:0,
		}
		,
		S2CInstanceTowerInfo: {
			M:0,
		}
		,
		C2SInstanceTowerFight: {
			Id:0,
		}
		,
		S2CInstanceTowerFight: {
			Tag:0,
			Id:0,
			Win:0,
		}
		,
		AllBossInfo: {
			Id:0,
			CurrHp:0,
			MaxHP:0,
			Num:0,
			Relive:0,
		}
		,
		S2CAllBossInfo: {
			Items:["AllBossInfo"],
		}
		,
		C2SAllBossStart: {
			Id:0,
		}
		,
		S2CAllBossStart: {
			Tag:0,
		}
		,
		C2SAllBossCfg: {
			Cfg:'',
		}
		,
		C2SAllBossRelive: {
			Id:0,
		}
		,
		S2CAllBossRelive: {
			Tag:0,
		}
		,
		C2SAllBossGetBuyInfo: {
		}
		,
		S2CAllBossGetBuyInfo: {
			Coin3:0,
			Times:0,
			LeftTimes:0,
			NoticeTimes:0,
			NoticeVip:0,
		}
		,
		C2SAllBossBuyTimes: {
		}
		,
		S2CAllBossBuyTimes: {
			Tag:0,
		}
		,
		DamageLog: {
			UserId:0,
			Nick:'',
			Damage:0,
		}
		,
		C2SAllBossGetDamageLog: {
			Id:0,
		}
		,
		S2CAllBossGetDamageLog: {
			Items:["DamageLog"],
		}
		,
		KillLog: {
			UserId:0,
			Nick:'',
			FightValue:0,
			KillTime:0,
		}
		,
		C2SAllBossGetKillLog: {
			Id:0,
		}
		,
		S2CAllBossGetKillLog: {
			Items:["KillLog"],
		}
		,
		AllBossV2Info: {
			Id:0,
			CurrHp:0,
			MaxHP:0,
			Num:0,
			Relive:0,
			RunAway:0,
			State:0,
		}
		,
		S2CAllBossV2Info: {
			Items:["AllBossV2Info"],
		}
		,
		C2SAllBossV2PlayerInBoss: {
		}
		,
		S2CAllBossV2PlayerInBoss: {
			BossId:0,
			DamageOrder:0,
			Damage:0,
		}
		,
		C2SAllBossV2Start: {
			Id:0,
		}
		,
		S2CAllBossV2Start: {
			Tag:0,
		}
		,
		C2SAllBossV2Cfg: {
			Cfg:'',
		}
		,
		C2SAllBossV2GetBuyInfo: {
		}
		,
		S2CAllBossV2GetBuyInfo: {
			Coin3:0,
			Times:0,
			LeftTimes:0,
			NoticeTimes:0,
			NoticeVip:0,
		}
		,
		C2SAllBossV2BuyTimes: {
		}
		,
		S2CAllBossV2BuyTimes: {
			Tag:0,
		}
		,
		DamageRank: {
			UserId:0,
			DamageTime:0,
			Nick:'',
			Damage:0,
			AreaId:0,
		}
		,
		C2SAllBossV2GetDamageLog: {
			Id:0,
		}
		,
		S2CAllBossV2GetDamageLog: {
			BossId:0,
			BossState:0,
			Items:["DamageRank"],
		}
		,
		K2SFightAllBossResult: {
			BossId:0,
			IsFirst:0,
			RunAwayTime:0,
		}
		,
		FieldBossV2Info: {
			Id:0,
			CurrHp:0,
			MaxHP:0,
			Num:0,
			Relive:0,
			RunAway:0,
			State:0,
		}
		,
		S2CFieldBossV2Info: {
			Items:["FieldBossV2Info"],
		}
		,
		C2SFieldBossV2PlayerInBoss: {
		}
		,
		S2CFieldBossV2PlayerInBoss: {
			BossId:0,
			DamageOrder:0,
			Damage:0,
		}
		,
		C2SFieldBossV2StartFight: {
			Id:0,
		}
		,
		S2CFieldBossV2StartFight: {
			Tag:0,
		}
		,
		C2SFieldBossV2Cfg: {
			Cfg:'',
		}
		,
		C2SFieldBossV2BuyTimes: {
		}
		,
		S2CFieldBossV2BuyTimes: {
			Tag:0,
		}
		,
		FieldBossDamageRank: {
			UserId:0,
			DamageTime:0,
			Nick:'',
			Damage:0,
		}
		,
		C2SFieldBossV2GetDamageLog: {
			Id:0,
		}
		,
		S2CFieldBossV2GetDamageLog: {
			BossId:0,
			BossState:0,
			Items:["FieldBossDamageRank"],
		}
		,
		FieldBossInfo: {
			Id:0,
			S:0,
			R:0,
			F:0,
			U:0,
			N:'',
			O:0,
			RoleId:0,
			Hp:0,
			MaxHp:0,
		}
		,
		S2CFieldBossInfo: {
			Items:["FieldBossInfo"],
		}
		,
		C2SFieldBossStart: {
			Id:0,
		}
		,
		S2CFieldBossStart: {
			Tag:0,
		}
		,
		C2SFieldBossItem: {
			Id:0,
		}
		,
		S2CFieldBossItem: {
			Ret:0,
		}
		,
		BossVipInfo: {
			Id:0,
			K:0,
			L:0,
		}
		,
		S2CBossVipInfo: {
			Items:["BossVipInfo"],
		}
		,
		C2SBossVipStart: {
			Id:0,
		}
		,
		S2CBossVipStart: {
			Tag:0,
			Id:0,
			Win:0,
			Sweep:0,
		}
		,
		Partner: {
			Id:0,
			T:0,
			L:0,
			E:0,
			AS:["Skill"],
			PS:["Skill"],
			PS2:["Skill"],
			R:0,
			B:0,
			S:0,
			SE:0,
			G:0,
			GE:0,
			N:'',
			H1:0,
			H2:0,
			H3:0,
			A1:0,
			A2:0,
			A3:0,
			D1:0,
			D2:0,
			D3:0,
			FightValue:0,
		}
		,
		C2SGetPartner: {
			Id:0,
			Type:0,
		}
		,
		S2CGetPartner: {
			P:"Partner",
		}
		,
		C2SPartnerLevelUp: {
			Id:0,
			Type:0,
			Auto:0,
		}
		,
		S2CPartnerLevelUp: {
			Tag:0,
			Id:0,
			Type:0,
			L:0,
			E:0,
			Multi:0,
		}
		,
		C2SPartnerStarUp: {
			Id:0,
			Type:0,
		}
		,
		S2CPartnerStarUp: {
			Tag:0,
			Id:0,
			Type:0,
			S:0,
			SE:0,
			ActiveSkill:"Skill",
		}
		,
		C2SPartnerGradeUp: {
			Id:0,
			Type:0,
		}
		,
		S2CPartnerGradeUp: {
			Tag:0,
			Id:0,
			Type:0,
			G:0,
			GE:0,
		}
		,
		C2SPartnerBattlePos: {
			Id:0,
			Type:0,
			Pos:0,
		}
		,
		S2CPartnerBattlePos: {
			Tag:0,
			Id:0,
			Type:0,
			RestId:0,
			Pos:0,
		}
		,
		C2SPartnerBattlePosRobot: {
			Type:0,
		}
		,
		C2SPartnerRefreshSkill: {
			Id:0,
			Type:0,
			RefreshType:0,
			LockSkills:["Skill"],
			Auto:0,
		}
		,
		S2CPartnerRefreshSkill: {
			Tag:0,
			Skills:["Skill"],
			Id:0,
			Type:0,
			RefreshType:0,
			LockSkills:["Skill"],
			Times:0,
		}
		,
		C2SPartnerDelSkill: {
			Id:0,
			SkillId:0,
		}
		,
		S2CPartnerDelSkill: {
			Tag:0,
			Id:0,
			Skills:["Skill"],
		}
		,
		C2SPartnerUpSkill: {
			Id:0,
			SkillId:0,
		}
		,
		S2CPartnerUpSkill: {
			Tag:0,
			Id:0,
			Skills:["Skill"],
		}
		,
		C2SPartnerReplaceSkill: {
			Id:0,
			Type:0,
		}
		,
		S2CPartnerReplaceSkill: {
			Tag:0,
			Id:0,
			Type:0,
			PS:["Skill"],
		}
		,
		C2SPartnerNick: {
			Id:0,
			Type:0,
			N:'',
		}
		,
		S2CPartnerNick: {
			Tag:0,
			Id:0,
			Type:0,
			N:'',
		}
		,
		C2SPartnerSuit: {
			Id:0,
		}
		,
		S2CPartnerSuit: {
			Tag:0,
			Id:0,
		}
		,
		C2SPartnerSuitRobot: {
		}
		,
		C2SUnlockNewPartner: {
			Id:0,
			Type:0,
		}
		,
		S2CUnlockNewPartner: {
			Tag:0,
			Id:0,
			Type:0,
		}
		,
		C2SStickPartner: {
			Id:0,
			Type:0,
		}
		,
		S2CStickPartner: {
			Tag:0,
			Id:0,
			Type:0,
		}
		,
		S2CPartnerNewSkill: {
			Type:0,
			Id:0,
			PS:["Skill"],
		}
		,
		Pet: {
			P:"Partner",
		}
		,
		S2CUserPet: {
			P:["Pet"],
			Suit:["int32"],
		}
		,
		PetA: {
			P:"Partner",
		}
		,
		S2CUserPetA: {
			P:["PetA"],
		}
		,
		Devil: {
			P:"Partner",
		}
		,
		S2CUserDevil: {
			P:["Devil"],
			DevilPos:["DevilPos"],
		}
		,
		C2SDevilAwake: {
		}
		,
		S2CDevilAwake: {
			Tag:0,
		}
		,
		C2SDevilLevelUp: {
			Id:0,
			Item:["ItemInfo"],
		}
		,
		S2CDevilLevelUp: {
			Tag:0,
			Id:0,
			L:0,
			E:0,
		}
		,
		DevilPos: {
			T:0,
			Id:0,
		}
		,
		C2SDevilPos: {
			T:0,
			Id:0,
		}
		,
		S2CDevilPos: {
			Tag:0,
			DevilPos:["DevilPos"],
		}
		,
		C2SDevilSoulPos: {
			Pos:0,
			Id:'',
		}
		,
		S2CDevilSoulPos: {
			Tag:0,
		}
		,
		C2SDevilSoulDel: {
			Id:[],
		}
		,
		S2CDevilSoulDel: {
			Tag:0,
		}
		,
		C2SDevilSoulAuto: {
			Auto:0,
		}
		,
		S2CDevilSoulAuto: {
			Tag:0,
		}
		,
		C2SDevilSoulLevelUp: {
			Id:'',
		}
		,
		S2CDevilSoulLevelUp: {
			Tag:0,
			Id:'',
			DevilSoul:"ItemData",
		}
		,
		C2SDevilSoulStarUp: {
			Id:'',
			SameId:[],
			AnyId:[],
		}
		,
		S2CDevilSoulStarUp: {
			Tag:0,
			Id:'',
			DevilSoul:"ItemData",
		}
		,
		Beauty: {
			P:"Partner",
			Magics:["BeautyMagic"],
			Cloth:["BeautyCloth"],
		}
		,
		BeautyMagic: {
			Id:0,
			Items:["int32"],
			TempItems:["int32"],
			Times:0,
		}
		,
		BeautyCloth: {
			Id:0,
			On:0,
			L:0,
		}
		,
		C2SBeautyNewCloth: {
			Id:0,
		}
		,
		S2CBeautyNewCloth: {
			Tag:0,
			Cloth:["BeautyCloth"],
		}
		,
		C2SBeautyWearCloth: {
			Id:0,
		}
		,
		S2CBeautyWearCloth: {
			Tag:0,
			Cloth:["BeautyCloth"],
		}
		,
		C2SBeautyClothUp: {
			Id:0,
		}
		,
		S2CBeautyClothUp: {
			Tag:0,
			Cloth:["BeautyCloth"],
		}
		,
		C2SBeautyFlySwitch: {
		}
		,
		S2CBeautyFlySwitch: {
			Tag:0,
		}
		,
		C2SBeautyEquip: {
			Level:0,
		}
		,
		S2CBeautyEquip: {
			Tag:0,
		}
		,
		C2SBeautyMagicRefresh: {
			Id:0,
			LockedItem:["int32"],
			Type:0,
		}
		,
		S2CBeautyMagicRefresh: {
			Tag:0,
			Id:0,
			TempItems:["int32"],
			Times:0,
		}
		,
		C2SBeautyMagicChange: {
			Id:0,
		}
		,
		S2CBeautyMagicChange: {
			Tag:0,
			Id:0,
			Items:["int32"],
			TempItems:["int32"],
		}
		,
		Kid: {
			P:"Partner",
			Good:0,
			T1:["int32"],
			T2:["int32"],
			T3:["int32"],
			T4:["int32"],
			TS:0,
		}
		,
		Toy: {
			Id:0,
			L:0,
		}
		,
		S2CUserKid: {
			P:["Kid"],
		}
		,
		S2CUserToy: {
			T:["Toy"],
		}
		,
		C2SKidTalentUp: {
			Id:0,
			Type:0,
			Part:0,
		}
		,
		S2CKidTalentUp: {
			Tag:0,
			Id:0,
			Type:0,
			Part:0,
			T:["int32"],
		}
		,
		C2SKidUseTalent: {
			Id:0,
			Type:0,
		}
		,
		S2CKidUseTalent: {
			Tag:0,
			Id:0,
			Type:0,
			TS:0,
			OldTS:0,
		}
		,
		C2SKidGoodUp: {
			Id:0,
		}
		,
		S2CKidGoodUp: {
			Tag:0,
			Id:0,
			Good:0,
		}
		,
		C2SToyLevelUp: {
			Id:0,
		}
		,
		S2CToyLevelUp: {
			Tag:0,
			Id:0,
			L:0,
		}
		,
		C2SToyOneKeyLevelUp: {
			Quality:0,
		}
		,
		S2CToyOneKeyLevelUp: {
			Tag:0,
			Quality:0,
			Toys:["Toy"],
		}
		,
		SkyGod: {
			P:"Partner",
		}
		,
		S2CUserSkyGod: {
			P:["SkyGod"],
		}
		,
		Beast: {
			P:"Partner",
		}
		,
		S2CUserBeast: {
			P:["Beast"],
		}
		,
		MasterPupilList: {
			U:0,
			N:'',
			L:0,
			OnlineState:0,
			IsMaster:0,
			IsGiveExp:0,
			RoleId:0,
		}
		,
		DbMasterInfo: {
			U:0,
			IsMaster:0,
			IsGiveExp:0,
		}
		,
		DbMasterData: {
			JoinTime:0,
			MasterList:["DbMasterInfo"],
			InviteList:["MasterArray"],
		}
		,
		S2CMasterMessage: {
			OutTime:0,
			List:["MasterPupilList"],
			InviteList:["MasterArray"],
		}
		,
		C2SSendMasterNotice: {
		}
		,
		S2CSendMasterNotice: {
			Tag:0,
		}
		,
		MasterArray: {
			U:0,
			N:'',
			L:0,
			D:0,
			S:0,
			T:0,
			DeleteTime:0,
		}
		,
		S2CMasterAds: {
			PupilList:["MasterArray"],
		}
		,
		C2SMasterInvite: {
			U:0,
		}
		,
		S2CMasterInvite: {
			Tag:0,
		}
		,
		S2CInvitePupil: {
			U:0,
			N:'',
		}
		,
		C2SHandleMasterInvite: {
			U:0,
			Y:0,
		}
		,
		S2CHandleMasterInvite: {
			Tag:0,
		}
		,
		C2SMasterGiveExp: {
			U:0,
		}
		,
		S2CMasterGiveExp: {
			Tag:0,
		}
		,
		C2SGetMasterExp: {
		}
		,
		S2CGetMasterExp: {
			Tag:0,
		}
		,
		C2SDeletePupil: {
			U:0,
		}
		,
		S2CDeletePupil: {
			Tag:0,
		}
		,
		C2SDeleteMaster: {
			T:0,
		}
		,
		S2CDeleteMaster: {
			Tag:0,
		}
		,
		MasterTask: {
			Id:0,
			State:0,
			Count:0,
		}
		,
		C2SGetPupilTask: {
			Id:0,
		}
		,
		S2CGetPupilTask: {
			Tag:0,
			Task:["MasterTask"],
		}
		,
		RoleMarryInfo: {
			MarryList:["DbMarryInfo"],
		}
		,
		DbMarryInfo: {
			U:0,
			T:0,
			H:0,
			HL:0,
			MV:0,
			HM:0,
			HouseUpList:["HouseUpList"],
			Pos:0,
			InviteList:["MarryInvite"],
			CanAddHouseValue:0,
		}
		,
		MarryInvite: {
			Pos:0,
			HouseId:0,
			InviteUserId:0,
		}
		,
		C2SGetMarryList: {
		}
		,
		MarryList: {
			U:0,
			N:'',
			L:0,
			F:0,
			R:0,
		}
		,
		S2CGetMarryList: {
			Tag:0,
			List:["MarryList"],
		}
		,
		C2SGetMarry: {
			I:0,
			P:0,
			U:0,
		}
		,
		S2CGetMarry: {
			Tag:0,
		}
		,
		S2CSendMarry: {
			I:0,
			N:'',
			L:0,
			F:0,
			R:0,
			M:0,
			U:0,
		}
		,
		C2SHandelMarry: {
			U:0,
			Y:0,
		}
		,
		S2CHandelMarry: {
			Tag:0,
		}
		,
		C2SSendFlower: {
			F:0,
			N:0,
			Bn:0,
		}
		,
		S2CSendFlower: {
			Tag:0,
			Mv:0,
		}
		,
		S2CGetFlower: {
			N:'',
			F:0,
			NM:0,
			MV:0,
		}
		,
		S2CMarryStatus: {
			U:0,
			N:'',
			R:0,
			D:0,
			M:0,
			HL:0,
			HS:0,
			MV:0,
			Pos:0,
			LT:0,
			List:["HouseUpList"],
		}
		,
		C2SMarryUpdate: {
		}
		,
		S2CMarryUpdate: {
			Tag:0,
		}
		,
		C2SHouseUpdate: {
			M:0,
		}
		,
		S2CHouseUpdate: {
			Tag:0,
		}
		,
		HouseUpList: {
			T:0,
			V:0,
		}
		,
		C2SGetUpdate: {
		}
		,
		S2CGetUpdate: {
			Tag:0,
		}
		,
		S2CNewMarry: {
			U:0,
			RM:0,
			RF:0,
			HN:'',
			WN:'',
		}
		,
		C2SSendMarryGift: {
			I:0,
			U:0,
		}
		,
		S2CSendMarryGift: {
			Tag:0,
		}
		,
		C2SDeleteWife: {
		}
		,
		S2CDeleteWife: {
			Tag:0,
		}
		,
		S2CMateOnline: {
			R:0,
		}
		,
		S2CQuizAsk: {
			Qid:0,
			Num:0,
			Aid:["int32"],
			AserTime:0,
			NextTime:0,
		}
		,
		S2CQuizRank: {
			RankList:["QuizRank"],
			Mn:0,
			Ms:0,
		}
		,
		QuizRank: {
			Nick:'',
			Score:0,
			Num:0,
			Id:0,
		}
		,
		C2SAnswerQuiz: {
			Aid:0,
		}
		,
		S2CAnswerQuiz: {
			Y:0,
		}
		,
		S2CQuizSum: {
			Mn:0,
			Ms:0,
			I:'',
		}
		,
		S2CQuizFirst: {
			Nick:'',
		}
		,
		S2CQuizStart: {
		}
		,
		DbPlayerWestExp: {
			List:["DbWestExp"],
		}
		,
		DbWestExp: {
			ProId:0,
			StartTime:0,
			RobberList:["RobbedPlayer"],
			IsHavePrize:0,
			Flag:0,
			Prize:["ItemInfo"],
			IsDouble:0,
			IsHaveSend:0,
			RobbedTimes:0,
			LogIncId:0,
			Idx:0,
			FinishTime:0,
			TotalRobberList:["RobbedPlayer"],
		}
		,
		RobbedPlayer: {
			EnemyId:0,
			ProId:0,
			Y:0,
			R:0,
			LogId:0,
			N:'',
			Idx:0,
		}
		,
		S2CWestExp: {
			Lt:0,
			Tt:0,
			Rl:0,
			Rt:0,
			IsHavePrize:0,
			St:0,
			RobberList:["RobbedPlayer"],
		}
		,
		ProtectingPlayer: {
			Uid:0,
			Rid:0,
			N:'',
			Gn:'',
			Fv:0,
			I:0,
			Pt:0,
			C:0,
		}
		,
		C2SGetProtectPlayer: {
		}
		,
		S2CGetProtectPlayer: {
			List:["ProtectingPlayer"],
		}
		,
		S2CNewProtectPlayer: {
			List:"ProtectingPlayer",
		}
		,
		S2CEndProtectPlayer: {
			Uid:0,
		}
		,
		C2SGetWestExp: {
			GetType:0,
		}
		,
		S2CGetWestExp: {
			Tag:0,
			I:0,
		}
		,
		C2SQuickFinishWestExp: {
		}
		,
		S2CQuickFinishWestExp: {
			Tag:0,
		}
		,
		C2SStartWestExp: {
		}
		,
		S2CStartWestExp: {
			Tag:0,
		}
		,
		S2CFinishWestExp: {
			I:0,
			P:'',
			List:["RobbedPlayer"],
			D:0,
		}
		,
		C2SGetWestPrize: {
		}
		,
		S2CGetWestPrize: {
			Tag:0,
		}
		,
		C2SGetRobbedList: {
		}
		,
		Robber: {
			U:0,
			Fv:0,
			N:'',
			I:0,
			Y:0,
			Lid:'',
			G:'',
			Uid:0,
		}
		,
		S2CGetRobbedList: {
			List:["Robber"],
		}
		,
		C2SSendRob: {
			U:0,
		}
		,
		S2CSendRob: {
			Tag:0,
		}
		,
		S2CBeRob: {
		}
		,
		C2SSendRevenge: {
			Lid:'',
		}
		,
		S2CSendRevenge: {
			Tag:0,
			P:'',
			Y:0,
			Lid:'',
		}
		,
		S2CWestExpStart: {
		}
		,
		Rank: {
			Id:0,
			R:0,
			A:["IntAttr"],
			B:["StrAttr"],
			SortValue:0,
			AdditionalOk:0,
		}
		,
		SimpleRank: {
			Id:0,
			R:0,
			Name:'',
			Level:0,
			Fv:0,
			SortValue:0,
			Vip:0,
			AdditionalOk:0,
		}
		,
		C2SAllRank: {
			Type:0,
			Param:0,
			FullDataRank:["int32"],
			SimpleDataRank:["int32"],
		}
		,
		S2CAllRank: {
			Type:0,
			Param:0,
			FullDataRank:["Rank"],
			SimpleDataRank:["SimpleRank"],
			MyData:"SimpleRank",
		}
		,
		C2SRespect: {
		}
		,
		S2CRespect: {
			Tag:0,
			Prize:["ItemInfo"],
		}
		,
		RankSortValue: {
			PlayerId:0,
			SortValue:0,
		}
		,
		NewRankDbInfo: {
			players:["Rank"],
			allplayersData:["RankSortValue"],
		}
		,
		GangDbMember: {
			UserId:0,
			JoinTime:0,
			DutyId:0,
		}
		,
		GangDbApply: {
			UserId:0,
			ApplyTime:0,
		}
		,
		GangDbAction: {
			Type:0,
			UserId:0,
			ActionTime:0,
			DutyId:0,
			Param:'',
		}
		,
		GangDbPeach: {
			EatTime:0,
			UserId:0,
			PeachId:0,
		}
		,
		GangDbInfo: {
			Id:'',
			Members:["GangDbMember"],
			Applys:["GangDbApply"],
			Name:'',
			Money:0,
			Level:0,
			NoticeId:0,
			NeedFightValue:0,
			Actions:["GangDbAction"],
			PeachVal:0,
			RecordPeach:["GangDbPeach"],
			AutoJoin:0,
			CreateServerId:0,
			CreateAreaId:0,
			RedPkgPool:0,
			GangFightVal:0,
		}
		,
		S2CGangRedPot: {
			A:0,
			E:0,
			P:0,
			G:0,
			kM:0,
		}
		,
		GangExchange: {
			NextReTime:0,
			ExchangeIds:["int32"],
			ReExchangeIds:["int32"],
			RefreshCount:0,
		}
		,
		C2SGangFindExchange: {
			GangId:'',
		}
		,
		S2CGangFindExchange: {
			Tag:0,
			Exchange:"GangExchange",
		}
		,
		C2SGangExchange: {
			GangId:'',
			ExcId:0,
		}
		,
		S2CGangExchange: {
			Tag:0,
			Exchange:"GangExchange",
		}
		,
		C2SGangRefreshExc: {
			GangId:'',
		}
		,
		S2CGangRefreshExc: {
			Tag:0,
			Exchange:"GangExchange",
		}
		,
		C2SGangSimpleInfo: {
			GId:'',
		}
		,
		S2CGangSimpleInfo: {
			Tag:0,
			GId:'',
			GN:'',
			GL:0,
		}
		,
		C2SGangInfo: {
			GangId:'',
		}
		,
		S2CGangInfo: {
			Gang:"GangInfo",
			Tag:0,
		}
		,
		GangInfo: {
			Name:'',
			MasterName:'',
			Nums:0,
			MaxNums:0,
			Money:0,
			UpMoney:0,
			Level:0,
			NoticeId:0,
			Id:'',
			NeedFightValue:0,
			IsJoin:0,
			MasterId:0,
			MasterRoleId:0,
			IsAutoJoin:0,
			GangFightVal:0,
			KM:0,
		}
		,
		C2SGangList: {
			Page:0,
		}
		,
		S2CGangList: {
			Gangs:["GangInfo"],
			Tag:0,
			Page:0,
			Row:0,
			Total:0,
			Prize:0,
		}
		,
		C2SCreateGang: {
			Name:'',
			Level:0,
		}
		,
		S2CCreateGang: {
			Tag:0,
			Gang:"GangInfo",
			DropId:0,
		}
		,
		C2SApplyGang: {
			GangId:'',
		}
		,
		S2CApplyGang: {
			GangId:'',
			Tag:0,
			IsJoin:0,
		}
		,
		C2SAgreeApplyGang: {
			UserId:0,
			GangId:'',
			ActionType:0,
		}
		,
		S2CAgreeApplyGang: {
			UserId:0,
			Tag:0,
			Nums:0,
			GangId:'',
			ActionType:0,
			RoleId:0,
			GN:'',
		}
		,
		C2SUpNoticeGang: {
			GangId:'',
			NoticeId:0,
		}
		,
		S2CUpNoticeGang: {
			GangId:'',
			NoticeId:0,
			Tag:0,
		}
		,
		C2SReNameGang: {
			GangId:'',
			Name:'',
		}
		,
		S2CReNameGang: {
			GangId:'',
			Name:'',
			Tag:0,
		}
		,
		GangMember: {
			UseId:0,
			Name:'',
			JoinTime:0,
			FightValue:0,
			Contribution:0,
			Status:0,
			DutyId:0,
			PreOutTime:0,
			RoleId:0,
			Level:0,
			MonthCard:0,
			YearCard:0,
			HisD:0,
		}
		,
		C2SGangMember: {
			GangId:'',
		}
		,
		S2CGangMember: {
			Members:["GangMember"],
			Tag:0,
		}
		,
		C2SGangApplys: {
			GangId:'',
		}
		,
		S2CGangApplys: {
			Applys:["GangMember"],
			Tag:0,
		}
		,
		C2SGangKickMasterConsumes: {
		}
		,
		S2CGangKickMasterConsumes: {
			Tag:0,
			Consumes:'',
		}
		,
		C2SGangKickMaster: {
			GangId:'',
			UserId:0,
		}
		,
		S2CGangKickMaster: {
			GangId:'',
			GangMasterId:0,
			Tag:0,
			Consumes:'',
			Nums:0,
			MasterName:'',
		}
		,
		C2SUpGangFightVal: {
			GangId:'',
			FightValue:0,
		}
		,
		S2CUpGangFightVal: {
			GangId:'',
			FightValue:0,
			Tag:0,
		}
		,
		C2SUpGangMoney: {
			Id:0,
			GangId:'',
		}
		,
		S2CUpGangMoney: {
			Money:0,
			GangId:'',
			Tag:0,
			Level:0,
		}
		,
		C2SGangRecruit: {
			UserId:0,
		}
		,
		S2CGangRecruit: {
			Tag:0,
		}
		,
		C2SGangGuardupLevel: {
			GangId:'',
		}
		,
		S2CGangGuardupLevel: {
			Tag:0,
			Level:0,
			Experience:0,
			Reward:["ItemInfo"],
		}
		,
		C2SGangUpDownDuty: {
			DutyId:0,
			UserId:0,
			GangId:'',
		}
		,
		S2CGangUpDownDuty: {
			Tag:0,
			DutyId:0,
			UserId:0,
		}
		,
		C2SGangKick: {
			UserId:0,
			GangId:'',
		}
		,
		S2CGangKick: {
			UserId:0,
			Tag:0,
			Nums:0,
		}
		,
		GangAction: {
			Type:0,
			UserId:0,
			Name:'',
			ActionTime:0,
			DutyId:0,
			Param:'',
		}
		,
		C2SGangRecord: {
			GangId:'',
		}
		,
		S2CGangRecord: {
			Tag:0,
			Actions:["GangAction"],
		}
		,
		C2SGangExit: {
			GangId:'',
		}
		,
		S2CGangExit: {
			Tag:0,
			Nums:0,
		}
		,
		C2SGangEnterMap: {
		}
		,
		S2CGangEnterMap: {
			Tag:0,
		}
		,
		C2SGangBossEnterMap: {
		}
		,
		S2CGangBossEnterMap: {
			Tag:0,
		}
		,
		C2SGangLeaveMap: {
		}
		,
		S2CGangLeaveMap: {
			Tag:0,
		}
		,
		C2SGangGuardReward: {
			Id:0,
			GangId:'',
		}
		,
		S2CGangGuardReward: {
			Tag:0,
			ReceiverId:["int32"],
			Id:0,
		}
		,
		C2SGangFindReward: {
			GangId:'',
		}
		,
		S2CGangFindReward: {
			Tag:0,
			ReceiverId:["int32"],
		}
		,
		C2SGangUpSkill: {
			GangId:'',
			SkillId:0,
		}
		,
		S2CGangUpSkill: {
			Tag:0,
			SkillId:0,
		}
		,
		C2SGangFindSkill: {
			GangId:'',
		}
		,
		S2CGangFindSkill: {
			CurSkill:0,
			CurHeiSkill:0,
			Skills:'',
			HeiSkills:'',
		}
		,
		GangPeachRecord: {
			Name:'',
			PeachId:0,
			EatTime:0,
		}
		,
		C2SGangFindPeach: {
			GangId:'',
		}
		,
		S2CGangFindPeach: {
			Tag:0,
			Records:["GangPeachRecord"],
			Boxs:'',
			Eats:'',
			GangPeachVal:0,
		}
		,
		C2SGangEatPeach: {
			GangId:'',
			PeachId:0,
		}
		,
		S2CGangEatPeach: {
			Tag:0,
			Records:["GangPeachRecord"],
			PeachId:0,
			Eats:'',
			GangPeachVal:0,
			GM:0,
		}
		,
		C2SGangPeachBox: {
			GangId:'',
			BoxId:0,
		}
		,
		S2CGangPeachBox: {
			Tag:0,
			BoxId:0,
			Boxs:'',
		}
		,
		C2SGangAutoJoin: {
			GangId:'',
			ActionType:0,
		}
		,
		S2CGangAutoJoin: {
			Tag:0,
			GangId:'',
			ActionType:0,
		}
		,
		C2SGangMapOnekeyOver: {
			GangId:'',
			TaskId:0,
		}
		,
		S2CGangMapOneKeyOver: {
			Tag:0,
			TaskId:0,
		}
		,
		C2SGangMapResetInfo: {
			GangId:'',
			TaskId:0,
		}
		,
		S2CGangMapResetInfo: {
			Tag:0,
			LeftTimes:0,
			NextVipTimes:0,
			NextVip:0,
			TaskId:0,
		}
		,
		C2SGangMapReset: {
			GangId:'',
			TaskId:0,
		}
		,
		S2CGangMapReset: {
			Tag:0,
			TaskId:0,
		}
		,
		RedPkgRecords: {
			Rds:["RedPkgRecord"],
		}
		,
		RedPkgRecord: {
			SId:0,
			M:0,
			T:0,
			Id:'',
			RId:0,
			RT:0,
			UT:0,
		}
		,
		C2SRedPkgPool: {
		}
		,
		S2CRedPkgPool: {
			Tag:0,
			LC:0,
			M:0,
		}
		,
		C2SRedPkgSend: {
			T:0,
			M:0,
			C:0,
			Des:'',
		}
		,
		S2CRedPkgSend: {
			Tag:0,
		}
		,
		S2CRedPkgNotice: {
			UN:'',
			ET:0,
			RId:'',
			URId:0,
			RT:0,
			Money:0,
		}
		,
		C2SRedPkgRecieve: {
			RId:'',
			RedType:0,
		}
		,
		S2CRedPkgRecieve: {
			RId:'',
			M:0,
			Tag:0,
			Desc:'',
			T:0,
		}
		,
		C2SRedPkgSingle: {
			RId:'',
		}
		,
		S2CRedPkgSingle: {
			Tag:0,
			UN:'',
			URId:0,
			Rds:["RedPkgShowRecord"],
			AM:0,
			Desc:'',
		}
		,
		RedPkgShowRecord: {
			SN:'',
			M:0,
			T:0,
			RT:0,
			RN:'',
			RId:0,
			SRId:0,
			RedId:'',
			RUId:0,
			UT:0,
		}
		,
		C2SRedPkgPerRecord: {
		}
		,
		S2CRedPkgPerRecord: {
			Rds:["RedPkgShowRecord"],
		}
		,
		RedPckLog: {
			Type:0,
			Money:0,
			Time:0,
		}
		,
		C2SRedPckShieldLog: {
		}
		,
		S2CRedPckShieldLog: {
			Logs:["RedPckLog"],
		}
		,
		GangSimpleInfo: {
			Id:'',
			Name:'',
			Level:0,
			MasterId:0,
			MasterName:'',
			CreateServerId:0,
			CreateAreaId:0,
			CurrServerId:0,
		}
		,
		S2KGangSimpleInfo: {
			Info:"GangSimpleInfo",
		}
		,
		JJCRole: {
			UserId:0,
			A:["IntAttr"],
			B:["StrAttr"],
			Kill:0,
			Robot:0,
		}
		,
		C2SJJCList: {
		}
		,
		S2CJJCList: {
			Tag:0,
			Roles:["JJCRole"],
		}
		,
		C2SJJCFight: {
			TargetId:0,
			TargetRank:0,
			Kill:0,
		}
		,
		S2CJJCFight: {
			Tag:0,
			IsWin:0,
			HonorPrize:0,
			Coin1Prize:0,
			Coin4Prize:0,
			HistoryRank:0,
			NowRank:0,
			Kill:0,
		}
		,
		C2SJJCGetBuyInfo: {
		}
		,
		S2CJJCGetBuyInfo: {
			Coin3:0,
			Times:0,
			LeftTimes:0,
			NoticeTimes:0,
			NoticeVip:0,
		}
		,
		C2SJJCBuyTimes: {
		}
		,
		S2CJJCBuyTimes: {
			Tag:0,
		}
		,
		C2SGodEquipAwake: {
			PartType:0,
		}
		,
		S2CGodEquipAwake: {
			Tag:0,
		}
		,
		C2SIntoSoulGradeUp: {
			PartType:0,
		}
		,
		S2CIntoSoulGradeUp: {
			Tag:0,
			Directly:0,
			Grade:"Grade",
		}
		,
		C2SGodForge: {
			PartType:0,
			Type:0,
			Times:0,
		}
		,
		S2CGodForge: {
			Tag:0,
			Grade1:"Grade",
			Grade2:"Grade",
		}
		,
		C2SGodForgeSave: {
			PartType:0,
		}
		,
		S2CGodForgeSave: {
			Tag:0,
			Grade1:"Grade",
			Grade2:"Grade",
		}
		,
		C2SGodEquipMelt: {
			ItemId:'',
		}
		,
		S2CGodEquipMelt: {
			Tag:0,
		}
		,
		ExpeditionTeamUnit: {
			UnitType:0,
			Hp:0,
			MaxHp:0,
			Pos:0,
			Id:0,
			State:0,
		}
		,
		ExpeditionData: {
			Team:["ExpeditionTeamUnit"],
			CurrId:0,
			CurrTeam:["ExpeditionTeamUnit"],
			CurrFightUserId:0,
			CheckPointRank:["int32"],
			Times:0,
			IsOpen:0,
			BoxState:0,
			DayResetTimes:0,
			Buff:["ExpeditionBuff"],
			GoodsId:0,
			DayMaxStage:0,
		}
		,
		TeamInfo: {
			TeamId:0,
			Nick:'',
			Num:0,
		}
		,
		MemberInfo: {
			UserId:0,
			Nick:'',
			Level:0,
			RoleId:0,
			Value:0,
			Leader:0,
		}
		,
		C2SGetTeamList: {
			InstanceType:0,
			InstanceId:0,
		}
		,
		S2CGetTeamList: {
			Tag:0,
			InstanceType:0,
			InstanceId:0,
			Teams:["TeamInfo"],
		}
		,
		C2SGetMemberList: {
			InstanceType:0,
			TeamId:0,
		}
		,
		S2CGetMemberList: {
			Tag:0,
			TeamId:0,
			Member:["MemberInfo"],
		}
		,
		S2KCrossChat: {
			Msg:"ChatMsg",
		}
		,
		K2SCrossChat: {
			Msg:"ChatMsg",
		}
		,
		K2SAddTmpTitle: {
			UserId:0,
			SkinId:0,
			Opp:0,
		}
		,
		C2SJoinInstance: {
			InstanceType:0,
			InstanceId:0,
			TeamId:0,
			Type:0,
		}
		,
		S2CJoinInstance: {
			Tag:0,
			TeamId:0,
			LeaderId:0,
			InstanceType:0,
		}
		,
		C2SLeaveInstance: {
			InstanceType:0,
			TeamId:0,
		}
		,
		S2CLeaveInstance: {
			Tag:0,
		}
		,
		C2SLeaveInstanceCopy: {
		}
		,
		S2CLeaveInstanceCopy: {
			Tag:0,
		}
		,
		C2SKillInstance: {
			InstanceType:0,
			TeamId:0,
			KillId:0,
		}
		,
		S2CKillInstance: {
			Tag:0,
		}
		,
		C2SStartInstance: {
			InstanceType:0,
			TeamId:0,
		}
		,
		S2CStartInstance: {
			Tag:0,
		}
		,
		Precious: {
			Id:'',
			IId:0,
			Quality:0,
			Pos:0,
			Skills:["Skill"],
			Lock:0,
			Creator:'',
			EatLevel:0,
			EatExp:0,
			SoulId:0,
			SoulLevel:0,
			SoulExp:0,
		}
		,
		PreciousPos: {
			Id:0,
			ForgeTime:0,
			Attr:["IntAttr"],
		}
		,
		C2SGetPreciousPos: {
		}
		,
		S2CGetPreciousPos: {
			Pos:["PreciousPos"],
		}
		,
		C2SCreatePrecious: {
			Type:0,
			Auto:0,
		}
		,
		S2CCreatePrecious: {
			Tag:0,
			NewPrecious:"Precious",
		}
		,
		C2SCreatePreciousFast: {
			Type:0,
		}
		,
		S2CCreatePreciousFast: {
			Tag:0,
			NewPrecious:["Precious"],
			Precious:["Precious"],
		}
		,
		C2SMeltPrecious: {
			Precious:[],
		}
		,
		S2CMeltPrecious: {
			Tag:0,
			Precious:[],
		}
		,
		C2SWearPrecious: {
			Id:'',
			Pos:0,
		}
		,
		S2CWearPrecious: {
			Tag:0,
			Id:'',
			Pos:0,
		}
		,
		C2SLockPrecious: {
			Id:'',
		}
		,
		S2CLockPrecious: {
			Tag:0,
			Id:'',
			Lock:0,
		}
		,
		C2SPreciousForge: {
			Pos:0,
			T:0,
			LockAttr:["int32"],
		}
		,
		S2CPreciousForge: {
			Tag:0,
			Pos:0,
			T:0,
			LockAttr:["int32"],
			Attr:["IntAttr"],
			Times:0,
		}
		,
		C2SPreciousEat: {
			Id:'',
			AutoBuy:0,
		}
		,
		S2CPreciousEat: {
			Tag:0,
			Id:'',
			EatLevel:0,
			EatExp:0,
		}
		,
		C2SPreciousSoul: {
			Id:'',
			SoulId:0,
		}
		,
		S2CPreciousSoul: {
			Tag:0,
			Id:'',
			SoulId:0,
		}
		,
		C2SPreciousSoulUp: {
			Id:'',
			SoulId:["ItemInfo"],
		}
		,
		S2CPreciousSoulUp: {
			Tag:0,
			Id:'',
			SoulLevel:0,
			SoulExp:0,
		}
		,
		C2SPreciousGive: {
			Id:'',
			Id2:'',
			T:0,
		}
		,
		S2CPreciousGive: {
			Tag:0,
			Id:'',
			Id2:'',
			T:0,
		}
		,
		C2SPreciousRobot: {
		}
		,
		S2CPreciousRobot: {
			Precious:["Precious"],
		}
		,
		GangWarIncGetDragonIds: {
		}
		,
		S2CActPlayerDeath: {
			UId:0,
		}
		,
		ReqActGangWarCDTime: {
			UId:0,
			MId:0,
		}
		,
		S2CActGangWarSettlement: {
			Gid:'',
			GN:'',
			GR:0,
			MR:0,
			SId:0,
		}
		,
		S2CGangWarBeforeCD: {
			CDs:["CDInfo"],
		}
		,
		CDInfo: {
			FId:0,
			SId:0,
			T:0,
		}
		,
		GangWarFightInfo: {
			Win:0,
			Pvp:0,
			AHV:0,
			PS:["GangWarPlayerInfo"],
			IncType:0,
			MId:0,
			EPS:["GangWarPlayerInfo"],
		}
		,
		GangWarPlayerInfo: {
			UId:0,
			HV:0,
		}
		,
		C2SActSountGateFight: {
		}
		,
		S2CActSountGateFight: {
			Tag:0,
		}
		,
		S2CSouthGateStatus: {
			HP:0,
			MHP:0,
		}
		,
		K2SActNineDayPreInfo: {
			Rk:["ActNineDayInfo"],
			DataType:0,
		}
		,
		C2SActNineDayPreInfo: {
		}
		,
		S2CActNineDayPreInfo: {
			PreInfo:"ActNineDayPreInfo",
		}
		,
		ActNineDayPreInfo: {
			Rk:["ActNineDayInfo"],
			WRk:["ActNineDayInfo"],
			Day:0,
		}
		,
		ActNineDayInfo: {
			SI:0,
			LD:0,
			SCV:0,
			UId:0,
			UN:'',
			A:["IntAttr"],
			B:["StrAttr"],
			CreateServerId:0,
		}
		,
		C2SActNineDaySort: {
		}
		,
		S2CActNineDaySort: {
			Rk:["ActNineDayInfo"],
			Tag:0,
		}
		,
		S2CNiceDayClose: {
			R:0,
			F:"ActNineDayInfo",
		}
		,
		K2SActGangWarPreInfo: {
			PreInfo:"ActGangWarPreInfo",
		}
		,
		C2SActGangWarPreInfo: {
		}
		,
		S2CActGangWarPreInfo: {
			PreInfo:"ActGangWarPreInfo",
		}
		,
		ActGangWarPreInfo: {
			SI:0,
			UId:0,
			UN:'',
			GId:'',
			GN:'',
			SCV:0,
		}
		,
		S2CActStart: {
			AId:0,
			OT:0,
		}
		,
		S2CActOver: {
			AId:0,
		}
		,
		S2CActIcon: {
			AIds:["ActIcon"],
		}
		,
		ActIcon: {
			Id:0,
			B:0,
			E:0,
		}
		,
		C2SActGangWarPersonRank: {
		}
		,
		S2CActGangWarPersonRank: {
			Tag:0,
			RS:["ActGangWarPersonRank"],
		}
		,
		ActGangWarPersonRank: {
			GN:'',
			UN:'',
			H:0,
			UId:0,
			SId:0,
		}
		,
		C2SActGangWarGangRank: {
		}
		,
		S2CActGangWarGangRank: {
			Tag:0,
			GS:["ActGangWarGangRank"],
		}
		,
		ActGangWarGangRank: {
			GN:'',
			H:0,
			GId:'',
			SId:0,
		}
		,
		C2SActGangWarKillRank: {
		}
		,
		S2CActGangWarKillRank: {
			Tag:0,
			KS:["ActGangWarKillRank"],
		}
		,
		ActGangWarKillRank: {
			GN:'',
			UN:'',
			K:0,
			UId:0,
			SId:0,
		}
		,
		C2SActGangWarScoreRank: {
		}
		,
		S2CActGangWarScoreRank: {
			Tag:0,
			SS:["ActGangWarScoreRank"],
		}
		,
		ActGangWarScoreRank: {
			GN:'',
			UN:'',
			S:0,
			UId:0,
			SId:0,
		}
		,
		C2SActGangWarScoreExchange: {
			Id:0,
		}
		,
		S2CActGangWarScoreExchange: {
			Tag:0,
			Ids:["int32"],
		}
		,
		C2SActGangWarScoreFind: {
		}
		,
		S2CActGangWarScoreFind: {
			Tag:0,
			Ids:["int32"],
		}
		,
		C2SActGangWarSwitchMap: {
			AT:0,
		}
		,
		S2CActGangWarSwitchMap: {
			Tag:0,
		}
		,
		S2CActGangWarInDragonGangs: {
			GS:["ActGangWarInDragonGang"],
		}
		,
		ActGangWarInDragonGang: {
			Gid:'',
			GN:'',
			SId:0,
		}
		,
		C2SActGiveUpDragon: {
		}
		,
		S2CActGiveUpDragon: {
			Tag:0,
		}
		,
		C2SActCreateTeam: {
			TeamType:0,
		}
		,
		S2CActCreateTeam: {
			Tag:0,
			TI:"ActTeamInfo",
		}
		,
		C2SActFindTeams: {
			TeamType:0,
		}
		,
		S2CActFindTeams: {
			TIS:["ActTeamInfo"],
			Tag:0,
		}
		,
		C2SActJoinTeam: {
			TId:0,
		}
		,
		S2CActJoinTeam: {
			Tag:0,
			TI:"ActTeamInfo",
		}
		,
		C2SActFindTeam: {
		}
		,
		S2CActFindTeam: {
			Tag:0,
			TI:"ActTeamInfo",
		}
		,
		C2SActNeedFightVal: {
			FV:0,
		}
		,
		S2CActNeedFightVal: {
			Tag:0,
			TI:"ActTeamInfo",
		}
		,
		C2SActExitTeam: {
			Uid:0,
		}
		,
		S2CActExitTeam: {
			Tag:0,
			Uid:0,
		}
		,
		ActTeamInfo: {
			Id:0,
			NFV:0,
			MS:["ActTeamMemInfo"],
		}
		,
		ActTeamMemInfo: {
			UId:0,
			UN:'',
			L:0,
			RId:0,
			LV:0,
			FV:0,
			MHP:0,
			HP:0,
		}
		,
		C2SActTeamRecruit: {
		}
		,
		S2CActTeamRecruit: {
			Tag:0,
		}
		,
		C2SJoinActive: {
			AId:0,
		}
		,
		S2CJoinActive: {
			Tag:0,
			AId:0,
		}
		,
		C2SLeaveActive: {
			AId:0,
		}
		,
		S2CLeaveActive: {
			Tag:0,
		}
		,
		K2SJoinFuncActiveHandleEvent: {
			ActType:0,
		}
		,
		S2CGangWarDragonPosition: {
			LUId:0,
			Us:["DragonPosition"],
			Mhp:0,
			Hp:0,
		}
		,
		DragonPosition: {
			UId:0,
			UN:'',
			SId:0,
			RId:0,
			Mhp:0,
			Hp:0,
		}
		,
		S2CGangWarGangSort: {
			Infos:["GangHurtInfo"],
			RId:0,
			V:0,
			T:0,
		}
		,
		GangHurtInfo: {
			GId:'',
			SId:0,
			GN:'',
			H:0,
			PC:0,
			APC:0,
			SC:0,
		}
		,
		S2CGangActWarGangPCount: {
			GId:'',
			PC:0,
		}
		,
		S2CGangActWarGangLocal: {
			L:0,
		}
		,
		S2CActiveGangWarScoreChange: {
			V:0,
		}
		,
		S2CActiveGangWarBeforeInsideScore: {
			V:0,
		}
		,
		C2SReviveLife: {
			ReviveType:0,
		}
		,
		S2CReviveLife: {
			Tag:0,
			ReviveType:0,
		}
		,
		C2SGangBossOverTime: {
		}
		,
		S2CGangBossOverTime: {
			Tag:0,
			OT:0,
			RT:0,
			S:0,
			AS:0,
		}
		,
		K2SAddGangRedPkgMoney: {
			M:0,
			Gid:'',
		}
		,
		C2SGangBossHurtSort: {
		}
		,
		S2CGangBossHurtSort: {
			HTs:["GangBossHurt"],
			Tag:0,
		}
		,
		S2CGangBossFiveHurt: {
			HTs:["GangBossHurt"],
		}
		,
		GangBossHurt: {
			Id:0,
			N:'',
			RId:0,
			Hurt:0,
		}
		,
		C2SGangBossKiller: {
		}
		,
		S2CGangBossKiller: {
			N:'',
			GangName:'',
		}
		,
		S2CGangBossRedPot: {
			RP:0,
		}
		,
		C2SGangCollect: {
		}
		,
		S2CGangCollect: {
			Tag:0,
		}
		,
		ActiveNineDayChangeInstance: {
		}
		,
		C2SActiveNineDayFindExchange: {
		}
		,
		S2CActiveNineDayFindExchange: {
			Tag:0,
			Id:0,
		}
		,
		S2CActiveNineDayScoreChange: {
			Tag:0,
			V:0,
		}
		,
		C2SActiveNineDayReviceExchange: {
			Id:0,
		}
		,
		S2CActiveNineDayReviceExchange: {
			Tag:0,
			Id:0,
		}
		,
		C2SGoldTree: {
		}
		,
		S2CGoldTree: {
			Tag:0,
			Gold:0,
			NextMulti:0,
		}
		,
		C2SGetGoldTreeInfo: {
		}
		,
		S2CGetGoldTreeInfo: {
			TodayUse:0,
			TodayTotal:0,
			NextVipTimes:0,
			ActId:0,
		}
		,
		NoticeUser: {
			Id:0,
			Nick:'',
			AreaId:0,
		}
		,
		NoticeItem: {
			Id:0,
			Num:0,
			OnlyId:'',
		}
		,
		NoticeFaBao: {
			IId:0,
			Id:'',
			Quality:0,
		}
		,
		S2CNotice: {
			Id:0,
			U:["NoticeUser"],
			I:["NoticeItem"],
			Stage:["int32"],
			P:[],
			FaBao:"NoticeFaBao",
			Skill:["int32"],
			ItemData:["ItemData"],
		}
		,
		DragonLogItem: {
			Time:0,
			UserId:0,
			InstanceId:0,
			Item:["ItemData"],
			DragonType:0,
		}
		,
		DragonLogItemClient: {
			Time:0,
			User:"NoticeUser",
			InstanceId:0,
			Item:["ItemData"],
		}
		,
		C2SGetDragonLog: {
			Type:0,
		}
		,
		S2CGetDragonLog: {
			Type:0,
			Logs:["DragonLogItemClient"],
		}
		,
		C2SStartFightDragon: {
			Id:0,
			Auto:0,
			ItemId:0,
			Num:0,
			Jump:0,
			Guide:0,
		}
		,
		S2CStartFightDragon: {
			Tag:0,
			Id:0,
			ItemId:0,
		}
		,
		MiracleItem: {
			Id:0,
			Multi:0,
			State:0,
		}
		,
		ActPlayerData: {
			GoodsInfo:["BuyGoods"],
			Log:["int32"],
			BossData:["PlayerBossData"],
			LuckDraw1:["LuckDraw"],
			PicturePart:["int32"],
			DareBossData:["PlayerDareBossData"],
			ActId:0,
			ReturnDetail:["ReturnDetail"],
			ReturnLog:["ChargeReturnLog"],
			ChargeReturnPrizeCache:"ChargeReturnPrizeCache",
			IntAttr:["IntAttr"],
			MiracleItem:["MiracleItem"],
		}
		,
		S2CContinueRechargeTaskNum: {
			Number:0,
		}
		,
		BuyGoods: {
			Gid:0,
			BuyTime:0,
			Status:0,
			BuyNumTimes:0,
		}
		,
		Activity: {
			ActId:0,
			Type:0,
			InitTime:0,
			StartTime:0,
			EndTime:0,
			Pos:0,
			Container:0,
			RedPoint:0,
			Name:'',
			Order:0,
			Icon:["ActivityIcon"],
			RMB:0,
			Param:0,
			Banner:0,
		}
		,
		ActivityIcon: {
			Pos:0,
			Container:0,
			Order:0,
			Icon:0,
			Circle:0,
		}
		,
		NoticePoint: {
			TimePoint:0,
			LoopNum:0,
			Sended:0,
		}
		,
		S2CAllActivity: {
			Activity:["Activity"],
		}
		,
		S2CActivityInit: {
			Activity:["Activity"],
		}
		,
		S2CActivityStart: {
			Activity:["Activity"],
		}
		,
		S2CActivityEnd: {
			ActId:["int32"],
		}
		,
		S2CActivityRedPoint: {
			ActId:0,
			RedPoint:0,
		}
		,
		S2CActivityIcon: {
			ActId:0,
			Pos:0,
			Icon:0,
		}
		,
		S2CActivityData: {
			S2CGetDrawData:["S2CGetDrawData"],
			S2CPlayerDrawData:["S2CPlayerDrawData"],
			S2CGetActShopList:["S2CGetActShopList"],
			S2CGetActGiftList:["S2CGetActGiftList"],
			S2CGetActBossInfo:["S2CGetActBossInfo"],
			S2CPlayerBossInfo:["S2CPlayerBossInfo"],
			S2CGetActTask:["S2CGetActTask"],
			S2CGetInvestInfo:["S2CGetInvestInfo"],
			S2CPlayerInvestData:["S2CPlayerInvestData"],
			S2CGetActRankInfo:["S2CGetActRankInfo"],
			S2CGetGoldTreeInfo:["S2CGetGoldTreeInfo"],
			S2CGetActPicture:["S2CGetActPicture"],
			S2CGetChargeReturnData:["S2CGetChargeReturnData"],
			S2CActRecoveryData:["S2CActRecoveryData"],
			S2CGetActGiftLimit:["S2CGetActGiftLimit"],
			S2CGetActGiftOnline:["S2CGetActGiftOnline"],
			S2CGetActGiftMiracle:["S2CGetActGiftMiracle"],
			S2CGetActGiftXRmbInfo:["S2CGetActGiftXRmbInfo"],
			S2CGetActCollectFontInfo:["S2CGetActCollectFontInfo"],
			S2CGetActLifeVipGiveInfo:["S2CGetActLifeVipGiveInfo"],
		}
		,
		C2SGetDrawData: {
			ActId:0,
		}
		,
		S2CGetDrawData: {
			ActId:0,
			Name:'',
			Luck:0,
			LowDraw:'',
			HighDrawOne:'',
			HighDrawTen:'',
			GaiID:0,
			Detail:["DrawDetail"],
			DoubleRate:0,
		}
		,
		DrawDetail: {
			ID:0,
			ItemId:0,
			ItemCount:0,
			TitleType:0,
			SortId:0,
			ShowRate:'',
			Name:'',
		}
		,
		C2SPlayerDrawData: {
			ActId:0,
		}
		,
		S2CPlayerDrawData: {
			Tag:0,
			ActId:0,
			Luck:0,
			Score:0,
			Free:0,
			MustDrawId:0,
			ActType:0,
			LuckDraw:["LuckDraw"],
			LowDrawTimes:0,
			DrawTimes:0,
			Discount:0,
		}
		,
		LuckDraw: {
			Value:0,
			Id:0,
			Have:0,
		}
		,
		C2SGetDrawListRate: {
			ActId:0,
		}
		,
		S2CGetDrawListRate: {
			ActId:0,
			Item:[],
			Rate:[],
		}
		,
		C2SBuyDrawItem: {
			ActId:0,
			Type:0,
			Count:0,
		}
		,
		S2CBuyDrawItem: {
			Tag:0,
			ActId:0,
		}
		,
		C2SDraw: {
			ActId:0,
			Type:0,
		}
		,
		S2CDraw: {
			Tag:0,
			ActId:0,
			Prize:["ItemInfo"],
			CritLuck:0,
			SpecialShow:["int32"],
		}
		,
		DrawLog: {
			Nick:'',
			DataId:0,
		}
		,
		DrawDbLog: {
			ActId:0,
			Nick:'',
			DataId:0,
		}
		,
		C2SGetDrawLog: {
			ActId:0,
		}
		,
		S2CGetDrawLog: {
			Tag:0,
			Log:["DrawLog"],
			ActId:0,
		}
		,
		C2SGetChargeReturnData: {
			ActId:0,
		}
		,
		S2CGetChargeReturnData: {
			ActId:0,
			IsReset:0,
			MaxTime:0,
			Mutil:["int32"],
		}
		,
		C2SPlayerChargeReturnData: {
			ActId:0,
		}
		,
		S2CPlayerChargeReturnData: {
			Tag:0,
			ActId:0,
			Times:0,
			TotalCharge:0,
			Need:0,
			Base:0,
			TodayUseTimes:0,
		}
		,
		ReturnDetail: {
			Item:0,
			T:0,
			Have:0,
		}
		,
		C2SChargeReturn: {
			ActId:0,
		}
		,
		S2CChargeReturn: {
			Tag:0,
			ActId:0,
			Multi:0,
		}
		,
		ChargeReturnPrizeCache: {
			Cached:0,
			Multi:0,
			Base:0,
		}
		,
		C2SChargeReturnReceivePrize: {
			ActId:0,
		}
		,
		S2CChargeReturnReceivePrize: {
			Tag:0,
			ActId:0,
			Multi:0,
		}
		,
		ChargeReturnLog: {
			Nick:'',
			Base:0,
			Multi:0,
		}
		,
		ChargeReturnDbLog: {
			ActId:0,
			Nick:'',
			Base:0,
			Multi:0,
		}
		,
		C2SGetChargeReturnLog: {
			ActId:0,
		}
		,
		S2CGetChargeReturnLog: {
			Tag:0,
			Log:["ChargeReturnLog"],
			ActId:0,
		}
		,
		C2SGetActShopList: {
			Aid:0,
		}
		,
		ActShopGoods: {
			Gid:0,
			F:0,
			Bt:0,
			T:0,
			Sort:0,
		}
		,
		S2CGetActShopList: {
			Aid:0,
			List:["ActShopGoods"],
		}
		,
		C2SActShopBuy: {
			Gid:0,
		}
		,
		S2CActShopBuy: {
			Tag:0,
			Gid:0,
			MsgParam:0,
			Goods:"ActShopGoods",
		}
		,
		C2SGetActGiftList: {
			Aid:0,
		}
		,
		ActGift: {
			Gid:0,
			Items:["ItemInfo"],
			CostType:0,
			CostNum:0,
			Price:0,
			Price2:0,
			Sort:0,
			CanBuy:0,
			Content:'',
			TabName:'',
			IsRec:0,
			OpenDay:0,
			OpenDayBuy:0,
			LoginDay:0,
			LoginDayBuy:0,
			UseYuan:0,
			DevilSoulgift:'',
			BuyTime:0,
			BuyTimeLimit:0,
		}
		,
		S2CGetActGiftList: {
			Aid:0,
			ActType:0,
			List:["ActGift"],
		}
		,
		C2SActGiftBuy: {
			Gid:0,
			Aid:0,
		}
		,
		S2CActGiftBuy: {
			Tag:0,
			P:'',
			Gid:0,
			Close:0,
			Aid:0,
		}
		,
		ActBossInfo: {
			Id:0,
			Name:'',
			Order:0,
			BossId:0,
			PetId:0,
			Prize:'',
		}
		,
		C2SGetActBossInfo: {
			ActId:0,
		}
		,
		S2CGetActBossInfo: {
			Tag:0,
			ActId:0,
			Info:["ActBossInfo"],
		}
		,
		PlayerBossData: {
			Id:0,
			State:0,
			Prize:0,
		}
		,
		C2SPlayerBossInfo: {
			ActId:0,
		}
		,
		S2CPlayerBossInfo: {
			Tag:0,
			ActId:0,
			Info:["PlayerBossData"],
		}
		,
		C2SFightActBoss: {
			ActId:0,
			BossId:0,
		}
		,
		S2CFightActBoss: {
			Tag:0,
			ActId:0,
			BossId:0,
			Prize:["ItemInfo"],
		}
		,
		C2SGetActBossPrize: {
			ActId:0,
			BossId:0,
		}
		,
		S2CGetActBossPrize: {
			Tag:0,
			ActId:0,
			BossId:0,
			Prize:["ItemInfo"],
		}
		,
		GiftLimitInfo: {
			GoodId:0,
			Items:["ItemInfo"],
			Price:0,
		}
		,
		C2SGetActGiftLimit: {
			AId:0,
		}
		,
		S2CGetActGiftLimit: {
			AId:0,
			List:["GiftLimitInfo"],
			ActType:0,
			ResId:0,
		}
		,
		GiftOnlineInfo: {
			GoodId:0,
			Items:["ItemInfo"],
			State:0,
			GetTime:0,
			OnlineTime:0,
		}
		,
		C2SGetActGiftOnline: {
			AId:0,
		}
		,
		S2CGetActGiftOnline: {
			AId:0,
			List:["GiftOnlineInfo"],
			Online:0,
		}
		,
		C2SActGetPrize: {
			AId:0,
			Param:0,
		}
		,
		S2CActGetPrize: {
			Tag:0,
			AId:0,
			Param:0,
		}
		,
		GiftMiracleInfo: {
			Id:0,
			Items:["ItemInfo"],
			BuyMul:0,
			State:0,
			CostItemId:0,
			CostItemNum:0,
		}
		,
		C2SGetActGiftMiracle: {
			AId:0,
		}
		,
		S2CGetActGiftMiracle: {
			AId:0,
			List:["GiftMiracleInfo"],
			T:0,
		}
		,
		GiftXRmbInfo: {
			Id:0,
			Items:["ItemInfo"],
			TableName:'',
			Icon:0,
			Bg:0,
			AdId:0,
			AnimId:0,
			State:0,
			AnimType:'',
		}
		,
		C2SGetActGiftXRmbInfo: {
			AId:0,
		}
		,
		S2CGetActGiftXRmbInfo: {
			AId:0,
			List:["GiftXRmbInfo"],
		}
		,
		CollectFontInfo: {
			Id:0,
			SourceItems:["ItemInfo"],
			TargetItems:["ItemInfo"],
			CurrTimes:0,
			MaxTimes:0,
		}
		,
		C2SGetActCollectFontInfo: {
			AId:0,
		}
		,
		S2CGetActCollectFontInfo: {
			AId:0,
			List:["CollectFontInfo"],
		}
		,
		C2SGetActLifeVipGiveInfo: {
			AId:0,
		}
		,
		S2CGetActLifeVipGiveInfo: {
			AId:0,
			State:0,
			Buy:0,
			Give:0,
			Price:0,
		}
		,
		ActDareBossInfo: {
			Id:0,
			Name:'',
			Order:0,
			BossId:0,
			ObjType:0,
			TypeId:0,
			Prize:'',
		}
		,
		C2SGetActDareBossInfo: {
			ActId:0,
		}
		,
		S2CGetActDareBossInfo: {
			Tag:0,
			ActId:0,
			Info:["ActDareBossInfo"],
		}
		,
		PlayerDareBossData: {
			Id:0,
			State:0,
			Prize:0,
		}
		,
		C2SPlayerDareBossInfo: {
			ActId:0,
		}
		,
		S2CPlayerDareBossInfo: {
			Tag:0,
			ActId:0,
			State:0,
			DeadLine:0,
			Info:["PlayerDareBossData"],
			EndTimestamp:0,
			ActIsOpen:0,
		}
		,
		DareState: {
			ActId:0,
			State:0,
		}
		,
		C2SPlayerDareState: {
		}
		,
		S2CPlayerDareState: {
			States:["DareState"],
		}
		,
		C2SPlayerDareIsOpen: {
		}
		,
		S2CPlayerDareIsOpen: {
			Open:0,
			EndTimestamp:["DareEndTimestamp"],
		}
		,
		DareEndTimestamp: {
			ActId:0,
			EndTimestamp:0,
			State:0,
		}
		,
		C2SFightActDareBoss: {
			ActId:0,
			BossId:0,
		}
		,
		S2CFightActDareBoss: {
			Tag:0,
			ActId:0,
			BossId:0,
			Prize:["ItemInfo"],
			Win:0,
		}
		,
		C2SGetActDareBossPrize: {
			ActId:0,
			BossId:0,
		}
		,
		S2CGetActDareBossPrize: {
			Tag:0,
			ActId:0,
			BossId:0,
			Prize:["ItemInfo"],
		}
		,
		C2SGetChargeMallList: {
			T:0,
		}
		,
		GoodsList: {
			Gid:0,
			Items:["ItemInfo"],
			Mt:0,
			S:0,
			P:0,
			Pid:0,
			Gt:0,
			I:0,
		}
		,
		S2CGetChargeMallList: {
			List:["GoodsList"],
		}
		,
		C2SChargeMallBuy: {
			Gid:0,
		}
		,
		S2CChargeMallBuy: {
			Tag:0,
			data:'',
		}
		,
		S2CPayNotify: {
			Type:0,
			GoodsID:0,
		}
		,
		ActTask: {
			Id:0,
			Name:'',
			Type:0,
			CounterType:0,
			Param:0,
			Target:0,
			Prize:'',
			Order:0,
			AnimId:'',
			ClientFuncId:0,
			RevelValue:0,
		}
		,
		C2SGetActTask: {
			ActId:0,
		}
		,
		S2CGetActTask: {
			Tag:0,
			ActId:0,
			Task:["ActTask"],
		}
		,
		S2CGetFirstRechargeTask: {
			Task:["ActTask"],
		}
		,
		GodTowerDayInfo: {
			Day:0,
			stage:0,
		}
		,
		C2SGetGodTowerActTaskDay: {
		}
		,
		S2CGetGodTowerActTaskDay: {
			Task:["GodTowerDayInfo"],
		}
		,
		C2SGetInvestInfo: {
			ActId:0,
		}
		,
		S2CGetInvestInfo: {
			Tag:0,
			ActId:0,
			AnimId:0,
			Task:["ActTask"],
			Name:'',
		}
		,
		C2SPlayerInvestData: {
			ActId:0,
		}
		,
		S2CPlayerInvestData: {
			Tag:0,
			ActId:0,
			Buy:0,
		}
		,
		C2SBuyInvest: {
			ActId:0,
		}
		,
		S2CBuyInvest: {
			Tag:0,
			ActId:0,
		}
		,
		RankData: {
			Id:0,
			RankMin:0,
			RankMax:0,
			BasePrize:'',
			AddPrize:'',
			PrizeTitle:'',
			PrizeContent:'',
			CompareParam:0,
		}
		,
		C2SGetActRankInfo: {
			ActId:0,
		}
		,
		S2CGetActRankInfo: {
			Tag:0,
			ActId:0,
			Name:'',
			RankType:0,
			Param:0,
			Data:["RankData"],
		}
		,
		C2SGetActRankData: {
			ActId:0,
			FullDataRank:["int32"],
			SimpleDataRank:["int32"],
		}
		,
		S2CGetActRankData: {
			ActId:0,
			FullDataRank:["Rank"],
			SimpleDataRank:["SimpleRank"],
			MyData:"SimpleRank",
		}
		,
		C2SGetActPicture: {
			ActId:0,
		}
		,
		S2CGetActPicture: {
			Tag:0,
			ActId:0,
			Task:["ActTask"],
			Pictures:["int32"],
			PicAnimId:0,
			PartCount:0,
			PartAnimId:["int32"],
			StickCount:0,
			Prize:0,
			Intro:0,
			PartItem:["int32"],
		}
		,
		C2SActPictureLight: {
			ActId:0,
			Part:0,
		}
		,
		S2CActPictureLight: {
			Tag:0,
			ActId:0,
			Pictures:["int32"],
		}
		,
		C2SActRecoveryData: {
			ActId:0,
		}
		,
		QualityPrice: {
			Quality:0,
			ItemInfo:["ItemInfo"],
		}
		,
		RecoverData: {
			LevelMin:0,
			LevelMax:0,
			Name:'',
			QualityPrice:["QualityPrice"],
			Id:0,
			StateLevel:0,
		}
		,
		S2CActRecoveryData: {
			items:["RecoverData"],
			FreeTimes:0,
			MonthTimes:0,
			ActId:0,
		}
		,
		C2SActRecoveryEquip: {
			ActId:0,
			Id:'',
			RecoveryId:0,
		}
		,
		S2CActRecoveryEquip: {
			Tag:0,
			Mul:["int32"],
		}
		,
		C2SShowItem: {
			Type:0,
			Param1:0,
			Param2:'',
			ChannelType:0,
		}
		,
		S2CShowItem: {
			Tag:0,
		}
		,
		S2CReportShowItem: {
			UserId:0,
			Nick:'',
			RoleId:0,
			GodLevel:0,
			Type:0,
			Param1:0,
			Param2:'',
			ChannelType:0,
			ShowId:0,
		}
		,
		C2SGetShowInfo: {
			UserId:0,
			Type:0,
			Param1:0,
			Param2:'',
			ShowId:0,
		}
		,
		S2CGetShowInfo: {
			Tag:0,
			Type:0,
			ItemInfo:"ItemData",
			PartnerInfo:"Partner",
			PreciousInfo:"Precious",
			GodEquip:["Grade"],
			PreciousPosInfo:"PreciousPos",
			ShowId:0,
		}
		,
		S2KShowItem: {
			ReportMsg:"S2CReportShowItem",
			ShowMsg:"S2CGetShowInfo",
		}
		,
		OnlineUser: {
			Id:0,
			Nick:'',
			Level:0,
		}
		,
		ApiResult: {
			Tag:0,
			Msg:'',
		}
		,
		C2SRedeemCode: {
			Code:'',
			ActivityId:0,
		}
		,
		S2CRedeemCode: {
			Tag:0,
			RewardList:'',
			ActivityId:0,
		}
		,
		C2SRealName: {
			Name:'',
			Card:'',
		}
		,
		S2CRealName: {
			Tag:0,
		}
		,
		C2SGetRealNamePrize: {
			RealName:0,
		}
		,
		S2CGetRealNamePrize: {
			Tag:0,
		}
		,
		S2CReportFcm: {
			FcmMinute:0,
			FcmStatus:0,
		}
		,
		C2SSetFcm: {
			Fcm:0,
		}
		,
		C2SGetLimitCloth: {
		}
		,
		S2CGetLimitCloth: {
			Tag:0,
		}
		,
		S2CGetLimitClothEnd: {
		}
		,
		BossHillBox: {
			BoxId:0,
			Pos:0,
			OpenTime:0,
		}
		,
		BossHillData: {
			Box:["BossHillBox"],
			TmpBoxId:0,
			Instances:["IntAttr"],
		}
		,
		S2CBossHillData: {
			Box:["BossHillBox"],
			Instances:["IntAttr"],
		}
		,
		C2SBossHillFight: {
			Id:0,
		}
		,
		S2CBossHillFight: {
			Tag:0,
			BoxId:0,
			Id:0,
			Win:0,
		}
		,
		C2SBossHillReplace: {
			Pos:0,
		}
		,
		S2CBossHillReplace: {
			Tag:0,
			BoxId:0,
			OpenTime:0,
			Pos:0,
		}
		,
		C2SBossHillOpen: {
			IsCoin:0,
			Pos:0,
		}
		,
		S2CBossHillOpen: {
			Tag:0,
			Pos:0,
			Items:["ItemData"],
		}
		,
		Instance81Data: {
			InstanceId:0,
			FirstTeam:[],
			FirstTime:0,
			FirstRound:0,
			QuickTeam:[],
			QuickTime:0,
			QuickRound:0,
		}
		,
		Player81Data: {
			InstanceId:0,
			State:0,
			FightTimes:0,
			IsBoxOpen:0,
		}
		,
		S2C81Info: {
			Data:["Player81Data"],
			HelpTimes:0,
		}
		,
		C2SViewInstance81Data: {
			InstanceId:0,
		}
		,
		S2CViewInstance81Data: {
			InstanceId:0,
			Instance:"Instance81Data",
		}
		,
		C2S81Sweep: {
			InstanceId:0,
			isSweepAll:0,
		}
		,
		S2C81Sweep: {
			Tag:0,
			InstanceId:["int32"],
			Items:["ItemData"],
		}
		,
		C2S81BuyBox: {
			Id:0,
			Chapter:0,
		}
		,
		S2C81BuyBox: {
			Tag:0,
			id:0,
			Chapter:0,
			Items:["ItemData"],
		}
		,
		GodItem: {
			Id:0,
			L:0,
			S:0,
			S2:0,
			SS1:0,
			SS2:0,
			SS3:0,
			S3:0,
			LS1:0,
			LS2:0,
			LS3:0,
			LS1T:0,
			LS2T:0,
			LS3T:0,
			S4:0,
			LSTIME:0,
			ST:0,
			STC:0,
		}
		,
		S2CAllGodItem: {
			GodItem:["GodItem"],
		}
		,
		C2SUnlockGodItem: {
			Id:0,
		}
		,
		S2CUnlockGodItem: {
			Tag:0,
			Id:0,
		}
		,
		C2SGodItemLevelUp: {
			Id:0,
		}
		,
		S2CGodItemLevelUp: {
			Tag:0,
			Id:0,
			Level:0,
		}
		,
		C2SGodItemSoulUp: {
			Id:0,
		}
		,
		S2CGodItemSoulUp: {
			Tag:0,
			Id:0,
			S:0,
		}
		,
		C2SGodItemSoul2Up: {
			Id:0,
		}
		,
		S2CGodItemSoul2Up: {
			Tag:0,
			Id:0,
			S2:0,
		}
		,
		C2SGodItemSoul2SkillUp: {
			Id:0,
			SkillId:0,
		}
		,
		S2CGodItemSoul2SkillUp: {
			Tag:0,
			Id:0,
			SkillId:0,
			SS:0,
		}
		,
		C2SGodItemSoul3Up: {
			Id:0,
		}
		,
		S2CGodItemSoul3Up: {
			Tag:0,
			Id:0,
			S3:0,
		}
		,
		C2SGodItemForge: {
			Id:0,
			Type:0,
			Times:0,
		}
		,
		S2CGodItemForge: {
			Tag:0,
			Id:0,
			Times:0,
			LS1T:0,
			LS2T:0,
			LS3T:0,
		}
		,
		C2SGodItemForgeSave: {
			Id:0,
		}
		,
		S2CGodItemForgeSave: {
			Tag:0,
			Id:0,
			LS1:0,
			LS2:0,
			LS3:0,
		}
		,
		C2SGodItemSoul4Up: {
			Id:0,
		}
		,
		S2CGodItemSoul4Up: {
			Tag:0,
			Id:0,
			S4:0,
		}
		,
		C2SGodItemStoneUp: {
			Id:0,
		}
		,
		S2CGodItemStoneUp: {
			Tag:0,
			Id:0,
			ST:0,
		}
		,
		C2SGodItemStoneCut: {
			Id:0,
		}
		,
		S2CGodItemStoneCut: {
			Tag:0,
			Id:0,
			STC:0,
		}
		,
		C2SKingInfo: {
		}
		,
		S2CKingInfo: {
			Tag:0,
			KingRank:0,
			Win:0,
			ContinueWin:0,
			Respect:0,
			Times:0,
			NextTime:0,
		}
		,
		C2SKingState: {
		}
		,
		S2CKingState: {
			State:0,
		}
		,
		KingMatch: {
			UserId:0,
			ServerId:0,
			RoleId:0,
			Nick:'',
			Rank:0,
		}
		,
		C2SKingMatch: {
		}
		,
		S2CKingMatch: {
			Tag:0,
			MatchList:["KingMatch"],
			Target:"KingMatch",
		}
		,
		C2SKingFight: {
		}
		,
		S2CKingFight: {
			Tag:0,
		}
		,
		KingRank: {
			KingRank:0,
			ServerId:0,
			Nick:'',
			Win:0,
			Sort:0,
		}
		,
		C2SKingRank: {
			Type:0,
		}
		,
		S2CKingRank: {
			Type:0,
			Rank:["KingRank"],
			MyRank:"KingRank",
		}
		,
		C2SKingPlayer: {
		}
		,
		S2CKingPlayer: {
			Id:0,
			ServerId:0,
			A:["IntAttr"],
			B:["StrAttr"],
		}
		,
		C2SKingGetBuyInfo: {
		}
		,
		S2CKingGetBuyInfo: {
			Coin3:0,
			Times:0,
			TotleTimes:0,
		}
		,
		C2SKingBuyTimes: {
		}
		,
		S2CKingBuyTimes: {
			Tag:0,
		}
		,
		C2SKingRespect: {
		}
		,
		S2CKingRespect: {
			Tag:0,
		}
		,
		WorldBossRankData: {
			UserId:0,
			Nick:'',
			RoleId:0,
			Damage:0,
			AreaId:0,
		}
		,
		S2CWorldBossRank5: {
			Rank:["WorldBossRankData"],
		}
		,
		S2CActWorldBossSettlement: {
			KillNick:'',
			KillGang:'',
			KillAreaId:0,
			FirstNick:'',
			FirstGang:'',
			FirstAreaId:0,
			Rank:0,
			IsKill:0,
			KillRoleId:0,
			FirstRoleId:0,
		}
		,
		WorldBossKillData: {
			UserId:0,
			Nick:'',
			GangName:'',
			AreaId:0,
		}
		,
		K2SWorldBossKillData: {
			Data:"WorldBossKillData",
		}
		,
		C2SGetWorldBossKillData: {
		}
		,
		S2CGetWorldBossKillData: {
			Data:"WorldBossKillData",
		}
		,
		S2CBossRefreshTime: {
			RefreshTime:0,
		}
		,
		C2SGetWorldBossRank: {
			Num:0,
		}
		,
		S2CGetWorldBossRank: {
			Rank:["WorldBossRankData"],
			SelfRank:0,
			SelfDamage:0,
		}
		,
		ExpeditionTeamMember: {
			UnitType:0,
			ObjId:0,
			Nick:'',
			CurrHp:0,
			MaxHp:0,
			RoleId:0,
			Pos:0,
			Level:0,
			FightValue:0,
			State:0,
		}
		,
		C2SExpeditionEnemy: {
		}
		,
		S2CExpeditionEnemy: {
			Items:["ExpeditionTeamMember"],
		}
		,
		C2SExpeditionTeam: {
		}
		,
		S2CExpeditionTeam: {
			Items:["ExpeditionTeamMember"],
		}
		,
		S2CExpeditionInfo: {
			CurrId:0,
			BoxId:["int32"],
			ResetTimes:0,
			ResetMaxTimes:0,
			Times:0,
			Buff1:"ExpeditionBuff",
			Buff2:"ExpeditionBuff",
			GoodsId:0,
			CanSweep:0,
		}
		,
		C2SExpeditionStartFight: {
		}
		,
		S2CExpeditionStartFight: {
			Tag:0,
			Win:0,
		}
		,
		C2SExpeditionGetPrize: {
			BoxId:0,
			Param:0,
		}
		,
		S2CExpeditionGetPrize: {
			Tag:0,
			BoxId:0,
			Param:0,
		}
		,
		C2SExpeditionRelive: {
		}
		,
		S2CExpeditionRelive: {
			Tag:0,
		}
		,
		C2SExpeditionReset: {
		}
		,
		S2CExpeditionReset: {
			Tag:0,
		}
		,
		C2SExpeditionSaveTeam: {
			UnitType:0,
			Ids:["int32"],
		}
		,
		S2CExpeditionSaveTeam: {
			Tag:0,
			Items:["ExpeditionTeamMember"],
		}
		,
		ExpeditionBuff: {
			Attr:["int32"],
			Use:0,
		}
		,
		C2SExpeditionRefreshBuff: {
			BoxId:0,
		}
		,
		S2CExpeditionRefreshBuff: {
			Tag:0,
			BoxId:0,
			Buff:"ExpeditionBuff",
		}
		,
		C2SExpeditionSweep: {
		}
		,
		S2CExpeditionSweep: {
			Tag:0,
		}
		,
		C2SWordsWear: {
			Part:0,
		}
		,
		S2CWordsWear: {
			Tag:0,
			Parts:["int32"],
		}
		,
		C2SWordsStick: {
			Part:0,
			Times:0,
			Level:0,
		}
		,
		S2CWordsStick: {
			Tag:0,
		}
		,
		C2SInstanceSoulHallFight: {
			Id:0,
		}
		,
		S2CInstanceSoulHallFight: {
			Tag:0,
			Id:0,
			Win:0,
		}
		,
		HolyBeast: {
			Id:0,
			HaveSoul:0,
			AttrId:0,
			ItemId:0,
			Level:0,
		}
		,
		C2SHolyBeast: {
			Param:0,
		}
		,
		S2CHolyBeast: {
			HolyBeasts:["HolyBeast"],
			SuitLevel:0,
			Param:0,
		}
		,
		C2SMixHolySoul: {
			Id:0,
			Type:0,
		}
		,
		S2CMixHolySoul: {
			Tag:0,
			MixOk:0,
			NewHolySoul:"ItemData",
		}
		,
		BreakHolySoulInfo: {
			Id:0,
			Num:0,
		}
		,
		C2SBreakHolySoul: {
			breaks:["BreakHolySoulInfo"],
		}
		,
		S2CBreakHolySoul: {
			Tag:0,
			breaks:["BreakHolySoulInfo"],
		}
		,
		C2SInjectHolySoul: {
			Id:0,
			Pos:0,
		}
		,
		S2CInjectHolySoul: {
			Tag:0,
			Id:0,
			Pos:0,
		}
		,
		InjectHolySoul: {
			Id:0,
			Pos:0,
		}
		,
		C2SOneKeyInjectHolySoul: {
			souls:["InjectHolySoul"],
		}
		,
		S2COneKeyInjectHolySoul: {
			Tag:0,
			souls:["InjectHolySoul"],
		}
		,
		C2SOutHolySoul: {
			Pos:0,
		}
		,
		S2COutHolySoul: {
			Tag:0,
			Id:0,
			Pos:0,
		}
		,
		StakeData: {
			UserId:0,
			Pos:0,
			StakeId:0,
			Win:0,
			FightId:0,
		}
		,
		GodClubDbData: {
			State:0,
			HandleState:["int32"],
			SignUpUser:["int32"],
			Session:0,
			Stakes:["StakeData"],
		}
		,
		GodClubUserInfo: {
			Session:0,
			BattlefieldId:0,
			UserId:0,
			Win:["int32"],
			Rank:0,
			Pos:0,
			Data:"BaseDbInfo",
		}
		,
		GodClubFightReportItem: {
			Win:0,
			GameIdx:0,
			Report:"S2CBattlefieldReport",
		}
		,
		GodClubFightReport: {
			Session:0,
			BattlefieldId:0,
			FightIdx:0,
			UserId1:0,
			UserId2:0,
			Items:["GodClubFightReportItem"],
		}
		,
		C2SGodClubSignUp: {
		}
		,
		S2CGodClubSignUp: {
			Tag:0,
		}
		,
		GodClubFightUser: {
			UserId:0,
			A:["IntAttr"],
			B:["StrAttr"],
		}
		,
		C2SGodClubFight: {
			FightIdx:0,
		}
		,
		S2CGodClubFight: {
			A:"GodClubFightUser",
			B:"GodClubFightUser",
			Win:["int32"],
			FightIdx:0,
		}
		,
		C2SGodClubFightReport: {
			FightIdx:0,
			GameIdx:0,
		}
		,
		S2CGodClubFightReport: {
			Tag:0,
		}
		,
		GodClub16User: {
			UserId:0,
			RoleId:0,
			Nick:'',
			AreaId:0,
			Pos:0,
			Win:["int32"],
		}
		,
		C2SGodClub16: {
		}
		,
		S2CGodClub16: {
			Users:["GodClub16User"],
			Stakes:["int32"],
		}
		,
		C2SGodClubStakeInfo: {
			FightId:0,
		}
		,
		S2CGodClubStakeInfo: {
			FightId:0,
			Pos:0,
			StakeId:0,
			A:"GodClubFightUser",
			B:"GodClubFightUser",
		}
		,
		C2SGodClubStake: {
			FightId:0,
			Pos:0,
			StackId:0,
		}
		,
		S2CGodClubStake: {
			Tag:0,
		}
		,
		C2SGodHerbEnter: {
			Id:0,
		}
		,
		S2CGodHerbEnter: {
			Tag:0,
			Id:0,
		}
		,
		C2SGodHerbRefresh: {
		}
		,
		S2CGodHerbRefresh: {
			Tag:0,
		}
		,
		C2SGodHerbCollect: {
			Id:0,
			Num:0,
			AutoBuy:0,
		}
		,
		S2CGodHerbCollect: {
			Tag:0,
			Num:0,
			Data:["ItemData"],
		}
		,
		C2SGetGodHerbLog: {
			Type:0,
		}
		,
		GodHerbLogItemClient: {
			Time:0,
			User:"NoticeUser",
			GodHerbId:0,
			Item:["ItemData"],
		}
		,
		S2CGetGodHerbLog: {
			Type:0,
			Logs:["GodHerbLogItemClient"],
		}
		,
		RobberyItem: {
			ItemId:0,
			RobberyId:0,
			Coin3:0,
		}
		,
		RobberyLogItem: {
			UserId:0,
			Nick:'',
			AreaId:0,
			Time:0,
			Pos:0,
			Mul:0,
			Coin3:0,
		}
		,
		S2CRobberyInfo: {
			Items:["RobberData"],
		}
		,
		C2SGetRobberyData: {
			ItemId:0,
			Num:0,
		}
		,
		S2CGetRobberyData: {
			Coin3:0,
			Id:0,
			Mul:0,
		}
		,
		C2SRobbery: {
			ItemId:0,
			Num:0,
			Pos:0,
		}
		,
		S2CRobbery: {
			Tag:0,
			Id:0,
			Items:["RobberData"],
		}
		,
		C2SRobberyLuck: {
			Id:0,
		}
		,
		S2CRobberyLuck: {
			Tag:0,
			Id:0,
		}
		,
		C2SGetRobberyLog: {
			Type:0,
		}
		,
		S2CGetRobberyLog: {
			Items:["RobberyLogItem"],
			Type:0,
		}
		,
		FairyMemoir: {
			MemoirId:0,
			State:0,
			IsReceivedPrize:0,
			NeedExploredSection:0,
		}
		,
		FairySheet: {
			SheetId:0,
			Memoirs:["FairyMemoir"],
		}
		,
		FairyAchievement: {
			AchievementId:0,
			State:0,
			Count:0,
			TimeStamp:0,
		}
		,
		S2CFairyMemoirTrigger: {
			SheetId:0,
			MemoirId:0,
			State:0,
		}
		,
		S2CFairyAchievementChange: {
			achievement:["FairyAchievement"],
		}
		,
		C2SFairyCharacterData: {
		}
		,
		S2CFairyCharacterData: {
			FightTimes:0,
			Sheets:["FairySheet"],
			achievement:["FairyAchievement"],
		}
		,
		C2SFairyCharacterFight: {
			SheetId:0,
			MemoirId:0,
			SectionId:0,
		}
		,
		S2CFairyCharacterFight: {
			Tag:0,
			SheetId:0,
			MemoirId:0,
			SectionId:0,
			Win:0,
		}
		,
		C2SFairyMemoirPrize: {
			SheetId:0,
			MemoirId:0,
		}
		,
		S2CFairyMemoirPrize: {
			Tag:0,
			SheetId:0,
			MemoirId:0,
			Items:["ItemData"],
		}
		,
		C2SFairyCharacterSweep: {
			SheetId:0,
			MemoirId:0,
		}
		,
		S2CFairyCharacterSweep: {
			Tag:0,
			SheetId:0,
			MemoirId:0,
			Items:["ItemData"],
		}
		,
		C2SFairyAchieventPrize: {
			AchieventId:0,
		}
		,
		S2CFairyAchieventPrize: {
			Tag:0,
			AchieventId:0,
			Items:["ItemData"],
		}
		,
		PlayerDragonTimes: {
			InstanceType:0,
			InstanceId:0,
			Times:0,
		}
		,
		PlayerDragon: {
			CloseStock:0,
			DragonOdd:0,
			TotalProfit:0,
			TotalProfits:["int32"],
			TotalFlowingWater:0,
			TotalFlowingWaters:["int32"],
			DayProfit:0,
			DayProfits:["int32"],
			DayFlowingWater:0,
			DayFlowingWaters:["int32"],
			TotalGuard:0,
			TotalRebate:0,
			DayGuard:0,
			DayRebate:0,
			CurrGuard:0,
			CurrRebate:0,
			MaxFlowingWater:0,
			DayMaxFlowingWater:0,
			Day:0,
			UserId:0,
			DragonTimes:["PlayerDragonTimes"],
			CurrNewGuard:0,
		}
		,
		ClientDb: {
			Key:'',
			Value:'',
		}
		,
		S2CClientDb: {
			Items:["ClientDb"],
		}
		,
		C2SClinetSet: {
			Key:'',
			Value:'',
		}
		,
		S2CClinetSet: {
			Tag:0,
		}
		,
		PassCheckItem: {
			Level:0,
			Prize1:0,
			Prize2:0,
		}
		,
		S2CPassCheckInfo: {
			Data:["PassCheckItem"],
		}
		,
		C2SPassCheckGetPrize: {
			Level:0,
			PrizeType:0,
		}
		,
		S2CPassCheckGetPrize: {
			Tag:0,
			Level:0,
			PrizeType:0,
		}
		,
		C2SPassCheckFast: {
			Exp:0,
		}
		,
		S2CPassCheckFast: {
			Tag:0,
		}
		,
		ZF: {
			Id:0,
			State:0,
			PetId:["int32"],
		}
		,
		C2SGetZF: {
		}
		,
		S2CGetZF: {
			Tag:0,
			ZF:["ZF"],
		}
		,
		C2SZFPetUp: {
			Id:0,
			Pos:0,
			PetId:0,
		}
		,
		S2CZFPetUp: {
			Tag:0,
			Id:0,
			Pos:0,
			PetId:0,
		}
		,
		C2SZFState: {
			Id:0,
			State:0,
		}
		,
		S2CZFState: {
			Tag:0,
			Id:0,
			State:0,
		}
		,
		C2SZFUnlock: {
			Id:0,
		}
		,
		S2CZFUnlock: {
			Tag:0,
			Id:0,
		}
		,
		RankDataItem: {
			RankKey:0,
			UserId:0,
			Value1:0,
			Value2:0,
			Ext:0,
		}
		,
		RankDbData: {
			RankData:["RankDataItem"],
		}
		,
		K2SNewEvent: {
			ServiceType:0,
			Count:0,
			Param2:0,
			Param3:0,
		}
		,
		C2SFuncOpen: {
			FuncId:0,
		}
		,
		S2CFuncOpen: {
			Tag:0,
		}
		,
		C2SRobotZF: {
		}
		,
		PhotoBook: {
			Id:0,
			L:0,
		}
		,
		S2CUserPhotoBook: {
			T:["PhotoBook"],
		}
		,
		C2SPhotoBookLevelUp: {
			Id:0,
		}
		,
		S2CPhotoBookLevelUp: {
			Tag:0,
			Id:0,
			L:0,
		}
		,
		C2SPhotoBookDel: {
		}
		,
		S2CPhotoBookDel: {
			Tag:0,
		}
		,
		C2SBuyChargeGift: {
			Id:0,
		}
		,
		S2CBuyChargeGift: {
			Tag:0,
			Id:0,
		}
		,
		S2CAllChargeGift: {
			List:["int32"],
		}
		,
		S2CVipService: {
			QQ:'',
		}
		,
		C2SNewPlayerZF: {
		}
		,
		C2SGetStateDemons: {
		}
		,
		S2CGetStateDemons: {
			Tag:0,
		}
		,
		C2SStateBreach: {
		}
		,
		S2CStateBreach: {
			Tag:0,
		}
		,
		C2SStateUsePill: {
		}
		,
		S2CStateUsePill: {
			Tag:0,
		}
		,
		C2SBuyStateGift: {
			Gift:0,
		}
		,
		S2CBuyStateGift: {
			Tag:0,
			Gift:0,
		}
		,
		C2SEquipStarInherit: {
			FromItemId:'',
			ToItemId:'',
		}
		,
		S2CEquipStarInherit: {
			Tag:0,
		}
		,
	}

return {
		PCK: values,
		toObj: function (k, d) {
			var b = msg[k];
			if(!b){return{}}
			var r = {};//补全后的数据
			for (var _k in b) {
                var _data=d[_k];
				//如果是数组
				if(b[_k] instanceof Array){
                	if(b[_k].length>0 && _data){
                        var arr=b[_k];//默认数组
                        var a_k=arr[0];//数组实例key
                        r[_k]=[];
                        for(var j=0;j<_data.length;j++){
                            if(a_k){
                            	if(a_k=="int32"){
                                    r[_k][j]=_data[j];
								}else{
                                    //根据返回数组中的每一项去完善数据
                                    r[_k][j]=this.toObj(a_k,_data[j])
								}
                            }
                        }
					}else{
                        r[_k]=[];
					}
				}else if(b[_k] && typeof (b[_k])=='string'){
                    _data=d[_k] || b[_k];
                    r[_k]=this.toObj(b[_k],_data)
				}else{
                    _data=d[_k] || b[_k];
                    r[_k] = _data
				}
			}
			return r;
		}
	}
}());

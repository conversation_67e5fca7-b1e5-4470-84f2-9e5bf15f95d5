{"name": "protobufjs", "version": "6.8.7", "versionScheme": "~", "description": "Protocol Buffers for JavaScript (& TypeScript).", "author": "<PERSON> <<EMAIL>>", "license": "BSD-3-<PERSON><PERSON>", "repository": {"type": "git", "url": "https://github.com/dcodeIO/protobuf.js.git"}, "bugs": "https://github.com/dcodeIO/protobuf.js/issues", "homepage": "http://dcode.io/protobuf.js", "keywords": ["protobuf", "protocol-buffers", "serialization", "typescript"], "main": "index.js", "browser": "dist/protobuf.js", "types": "index.d.ts", "bin": {"pbjs": "bin/pbjs", "pbts": "bin/pbts"}, "scripts": {"bench": "node bench", "build": "gulp --gulpfile scripts/gulpfile.js", "changelog": "node scripts/changelog -w", "coverage": "istanbul --config=config/istanbul.json cover node_modules/tape/bin/tape tests/*.js tests/node/*.js", "coverage-ci": "npm run coverage && codeclimate-test-reporter < coverage/lcov.info", "docs": "jsdoc -c config/jsdoc.json -R README.md --verbose --pedantic", "lint": "eslint **/*.js -c config/eslint.json && tslint **/*.d.ts -e **/node_modules/** -t stylish -c config/tslint.json", "pages": "node scripts/pages", "prepublish": "node scripts/prepublish", "postinstall": "node scripts/postinstall", "prof": "node bench/prof", "test": "tape -r ./lib/tape-adapter tests/*.js tests/node/*.js | tap-spec", "test-types": "tsc tests/comp_typescript.ts --lib es2015 --strictNull<PERSON>hecks --experimentalDecorators --emitDecoratorMetadata && tsc tests/data/test.js.ts --lib es2015 --noEmit --strictNullChecks && tsc tests/data/rpc.ts --lib es2015 --noEmit --strictNullChecks", "types": "node bin/pbts --main --global protobuf --out index.d.ts src/ lib/aspromise/index.js lib/base64/index.js lib/codegen/index.js lib/eventemitter/index.js lib/float/index.js lib/fetch/index.js lib/inquire/index.js lib/path/index.js lib/pool/index.js lib/utf8/index.js && npm run test-types", "make": "npm run test && npm run types && npm run build && npm run lint", "release": "npm run make && npm run changelog"}, "dependencies": {"@protobufjs/aspromise": "^1.1.2", "@protobufjs/base64": "^1.1.2", "@protobufjs/codegen": "^2.0.4", "@protobufjs/eventemitter": "^1.1.0", "@protobufjs/fetch": "^1.1.0", "@protobufjs/float": "^1.0.2", "@protobufjs/inquire": "^1.1.0", "@protobufjs/path": "^1.1.2", "@protobufjs/pool": "^1.1.0", "@protobufjs/utf8": "^1.1.0", "@types/long": "^3.0.32", "@types/node": "^8.9.4", "long": "^4.0.0"}, "devDependencies": {"benchmark": "^2.1.4", "browserify": "^14.5.0", "browserify-wrap": "^1.0.2", "bundle-collapser": "^1.3.0", "chalk": "^1.1.3", "codeclimate-test-reporter": "^0.5.0", "escodegen": "^1.9.0", "eslint": "^3.19.0", "espree": "^3.5.3", "estraverse": "^4.2.0", "gh-pages": "^0.12.0", "git-raw-commits": "^1.3.2", "git-semver-tags": "^1.3.2", "glob": "^7.1.2", "google-protobuf": "^3.5.0", "gulp": "^3.9.1", "gulp-header": "^1.8.9", "gulp-if": "^2.0.1", "gulp-sourcemaps": "^2.6.4", "gulp-uglify": "^2.1.2", "istanbul": "^0.4.5", "jaguarjs-jsdoc": "github:dcodeIO/jaguarjs-jsdoc", "jsdoc": "^3.5.5", "minimist": "^1.2.0", "reflect-metadata": "^0.1.12", "semver": "^5.5.0", "tap-spec": "^4.1.1", "tape": "^4.9.0", "tmp": "0.0.33", "tslint": "^5.9.1", "typescript": "^2.7.2", "uglify-js": "^2.8.29", "vinyl-buffer": "^1.0.1", "vinyl-fs": "^2.4.4", "vinyl-source-stream": "^1.1.2"}, "cliDependencies": ["semver", "chalk", "glob", "jsdoc", "minimist", "tmp", "uglify-js", "espree", "escodegen", "estraverse"], "files": ["index.js", "index.d.ts", "light.d.ts", "light.js", "minimal.d.ts", "minimal.js", "package-lock.json", "tsconfig.json", "scripts/postinstall.js", "bin/**", "cli/**", "dist/**", "ext/**", "google/**", "src/**"]}
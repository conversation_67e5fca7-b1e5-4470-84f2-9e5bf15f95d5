// Copyright 2013 The Gorilla WebSocket Authors. All rights reserved.
// Use of this source code is governed by a BSD-style
// license that can be found in the LICENSE file.

package websocket

import (
	"bytes"
	"encoding/json"
	"io"
	"reflect"
	"testing"
)

func TestJSON(t *testing.T) {
	var buf bytes.Buffer
	wc := newTestConn(nil, &buf, true)
	rc := newTestConn(&buf, nil, false)

	var actual, expect struct {
		A int
		B string
	}
	expect.A = 1
	expect.B = "hello"

	if err := wc.WriteJSON(&expect); err != nil {
		t.Fatal("write", err)
	}

	if err := rc.ReadJSON(&actual); err != nil {
		t.Fatal("read", err)
	}

	if !reflect.DeepEqual(&actual, &expect) {
		t.Fatal("equal", actual, expect)
	}
}

func TestPartialJSONRead(t *testing.T) {
	var buf0, buf1 bytes.Buffer
	wc := newTestConn(nil, &buf0, true)
	rc := newTestConn(&buf0, &buf1, false)

	var v struct {
		A int
		B string
	}
	v.A = 1
	v.B = "hello"

	messageCount := 0

	// Partial JSON values.

	data, err := json.Marshal(v)
	if err != nil {
		t.Fatal(err)
	}
	for i := len(data) - 1; i >= 0; i-- {
		if err := wc.WriteMessage(TextMessage, data[:i]); err != nil {
			t.Fatal(err)
		}
		messageCount++
	}

	// Whitespace.

	if err := wc.WriteMessage(TextMessage, []byte(" ")); err != nil {
		t.Fatal(err)
	}
	messageCount++

	// Close.

	if err := wc.WriteMessage(CloseMessage, FormatCloseMessage(CloseNormalClosure, "")); err != nil {
		t.Fatal(err)
	}

	for i := 0; i < messageCount; i++ {
		err := rc.ReadJSON(&v)
		if err != io.ErrUnexpectedEOF {
			t.Error("read", i, err)
		}
	}

	err = rc.ReadJSON(&v)
	if _, ok := err.(*CloseError); !ok {
		t.Error("final", err)
	}
}

func TestDeprecatedJSON(t *testing.T) {
	var buf bytes.Buffer
	wc := newTestConn(nil, &buf, true)
	rc := newTestConn(&buf, nil, false)

	var actual, expect struct {
		A int
		B string
	}
	expect.A = 1
	expect.B = "hello"

	if err := WriteJSON(wc, &expect); err != nil {
		t.Fatal("write", err)
	}

	if err := ReadJSON(rc, &actual); err != nil {
		t.Fatal("read", err)
	}

	if !reflect.DeepEqual(&actual, &expect) {
		t.Fatal("equal", actual, expect)
	}
}
